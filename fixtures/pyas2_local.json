[{"model": "pyas2.organization", "pk": "sinoflex-local-as2", "fields": {"name": "SinoLocal", "email_address": "<EMAIL>", "encryption_key": 2, "signature_key": 2, "confirmation_message": ""}}, {"model": "pyas2.partner", "pk": "sinoflex-uat-as2", "fields": {"name": "SinoUAT", "email_address": null, "http_auth": false, "http_auth_user": null, "http_auth_pass": null, "https_verify_ssl": true, "target_url": "https://sinoflex-uat.cratepilot.com/pyas2/as2receive", "subject": "EDI Message sent using pyas2", "content_type": "application/edi-consent", "compress": false, "encryption": "tripledes_192_cbc", "encryption_cert": 3, "signature": "sha256", "signature_cert": 3, "mdn": true, "mdn_mode": "SYNC", "mdn_sign": "sha256", "confirmation_message": "", "keep_filename": false, "cmd_send": "", "cmd_receive": ""}}, {"model": "pyas2.partner", "pk": "test-local-as2", "fields": {"name": "TestLocal", "email_address": null, "http_auth": false, "http_auth_user": null, "http_auth_pass": null, "https_verify_ssl": true, "target_url": "http://127.0.0.1:8080/pyas2/as2receive", "subject": "EDI Message sent using pyas2", "content_type": "application/edi-consent", "compress": false, "encryption": "tripledes_192_cbc", "encryption_cert": 2, "signature": "sha256", "signature_cert": 2, "mdn": true, "mdn_mode": "SYNC", "mdn_sign": "sha256", "confirmation_message": "", "keep_filename": false, "cmd_send": "", "cmd_receive": ""}}, {"model": "pyas2.privatekey", "pk": 2, "fields": {"name": "SinoLocal_20260101_private.pem", "key": "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", "key_pass": "sinoflex", "valid_from": "2025-04-27T15:21:03Z", "valid_to": "2036-04-09T15:21:03Z", "serial_number": "352420229992276268057003423588324495147799070883"}}, {"model": "pyas2.publiccertificate", "pk": 2, "fields": {"name": "TestLocal_20260101_public.pem", "certificate": "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURhekNDQWxPZ0F3SUJBZ0lVUk50enh6eXNWajhOU3VtZk9OOEFpNzZNdmY4d0RRWUpLb1pJaHZjTkFRRUwKQlFBd1JURUxNQWtHQTFVRUJoTUNRVlV4RXpBUkJnTlZCQWdNQ2xOdmJXVXRVM1JoZEdVeElUQWZCZ05WQkFvTQpHRWx1ZEdWeWJtVjBJRmRwWkdkcGRITWdVSFI1SUV4MFpEQWVGdzB5TlRBME1qY3hOVEl5TkRoYUZ3MHpOVEEwCk1qVXhOVEl5TkRoYU1FVXhDekFKQmdOVkJBWVRBa0ZWTVJNd0VRWURWUVFJREFwVGIyMWxMVk4wWVhSbE1TRXcKSHdZRFZRUUtEQmhKYm5SbGNtNWxkQ0JYYVdSbmFYUnpJRkIwZVNCTWRHUXdnZ0VpTUEwR0NTcUdTSWIzRFFFQgpBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRREFtVkQ1N3VZNlNMM3g1SStWMGlobUdwZzFyZHRCWE1Fb294R25lRll5CjFWYXQ0YjhzOE55SzZDTmJVQ2dEQ3VENU1LTkgrQ2FBMFU3K3J6azdSRDVQV0lteVo0cVhMTjZnR2RGaURHWFoKYzNZbU11eldBWVVBamJBUHVSU3hOSCtKSXBlQ2FaZmFkKzV3b2k4NkxjSEl3eWZUQ0YyL09neGl3SXNzRU9aYQpuQ3hjNlJLQktwUWNJQ0ZPa1p0c3gwL05ETENFYUNiR2ZQd3hMVjZKWGtvVHBqZ2JPS0g2NHZkeExzc0w1S1JuCmVLL2ZOeFVweFFyeUpHSHg4QWZSVVdBampFZmtmdWhOQ3VxMWdaS0xkajZGb1NUNUFaSUlPT2hkN2lOajFCaWcKR1pDemY5UEU2VXhaczZ0K05qV2JHbjNsS2daR0RSQ0FoZ2NwY3E2dmwyK0hBZ01CQUFHalV6QlJNQjBHQTFVZApEZ1FXQkJRNW5QcklwMjZNVlF3UmpJRUxpNHBFTzc0WmpEQWZCZ05WSFNNRUdEQVdnQlE1blBySXAyNk1WUXdSCmpJRUxpNHBFTzc0WmpEQVBCZ05WSFJNQkFmOEVCVEFEQVFIL01BMEdDU3FHU0liM0RRRUJDd1VBQTRJQkFRQjcKdHE0QXFrT1pQNlgvRU9wNE9wYTJsZDB1enV5MXNNZWNzWEJ2Rm1ZYkh0ZG55bGZWOS9DR0NWek5GQXk2QUtteApMUzVHSWJTTGxSSk1nZDdndnBtSUk3Nm92S0puVTVLVHE3Z3NHVVhTU1dxcndQYU5HS3pOVm80UXc2RjdvVWV1CkpIMWpweHZnYm5mWFVpb2hZRzNEbEhONHRCQ1NIUWN5eEVRbnFGSVZvRy9WYitIUk1ncUlpRE9FUER1bktiMjIKbUFSOGljWmlPQUtoS1QzUCtHV3Z2N3Zhb1BtYWw4ZFg0Rm9Ka1BsWnZiNU1kdE10ckc2dElTQTlueFk4dkJyZwpJM2REWDFZb2cxZ2RMUUltZVE3MmhzOVZXVnlEaFNZcVNFeUNSQ1drV0VvV1lCVWpaKzlrckRFcFVOcElmeTdVCkJMbDRNZmZQRU0yZU8zZ1BMR256Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K", "certificate_ca": null, "verify_cert": true, "valid_from": "2025-04-27T15:22:48Z", "valid_to": "2035-04-25T15:22:48Z", "serial_number": "393105321324220967900445661007457514871893835263"}}, {"model": "pyas2.publiccertificate", "pk": 3, "fields": {"name": "SinoUAT_20350701_public.pem", "certificate": "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURhekNDQWxPZ0F3SUJBZ0lVSjNKb0gxR2FJMUVrU3RQamovWE5JNkJjZXZNd0RRWUpLb1pJaHZjTkFRRUwKQlFBd1JURUxNQWtHQTFVRUJoTUNRVlV4RXpBUkJnTlZCQWdNQ2xOdmJXVXRVM1JoZEdVeElUQWZCZ05WQkFvTQpHRWx1ZEdWeWJtVjBJRmRwWkdkcGRITWdVSFI1SUV4MFpEQWVGdzB5TlRBNE1UUXdNVEF6TXpsYUZ3MHpOVEE0Ck1USXdNVEF6TXpsYU1FVXhDekFKQmdOVkJBWVRBa0ZWTVJNd0VRWURWUVFJREFwVGIyMWxMVk4wWVhSbE1TRXcKSHdZRFZRUUtEQmhKYm5SbGNtNWxkQ0JYYVdSbmFYUnpJRkIwZVNCTWRHUXdnZ0VpTUEwR0NTcUdTSWIzRFFFQgpBUVVBQTRJQkR3QXdnZ0VLQW9JQkFRREhUalh5VVlXMEdFaDBBVTBHbmh2QzFLaHVBL3ZpOEw2N2VRNWJJVFdJCk1iUlFWaHJrZzBMKzV6RXNTQ3FLQ201RFBsTGdjZExhZXhuaGpRMlNKc29Ud1N2ZXBEYVg3NGZBUVIxc2NsbTUKVzNDZ3N0dCtEdGNjdXJrRHo3b09kakFJblE2Mi9saWxtcFhNV2ZuOEpUbjlzc1pER0dDK3B4ZFhjWnlEdzBMbwpIVDl0eHJGSDQyWFVWcU1DMGpzYUUvRUdNTlRZd01vY0xheVhnVWZZL1VsR0RuMitxUnY5U3pEZks0aWxMUEc1CldmK0FtUW9xWDd2djJnTjhSZThGRWgwWlJnU3BLWUlmR2Ezck41bjFZVEp4TGMrT0ViK0ZWRE04K25tZmZFSWYKK0w4WUZLaGtJV3BqUEN5b2xtY0RhaG1lbzcwOEZXSVkrMTN1Zml5OUhvaXJBZ01CQUFHalV6QlJNQjBHQTFVZApEZ1FXQkJSeFIrUkFUeGtvd2tLdVZYWHI3WnpPRG1TS3FEQWZCZ05WSFNNRUdEQVdnQlJ4UitSQVR4a293a0t1ClZYWHI3WnpPRG1TS3FEQVBCZ05WSFJNQkFmOEVCVEFEQVFIL01BMEdDU3FHU0liM0RRRUJDd1VBQTRJQkFRQUwKbDBsUjBjOWhQZEkyaDRwcDh3U292bVd4RUJDNkNhNHNEM0xLVDRQN2NVdE5xOVU0Y0pDcWVFTzFtSEZQM3dWLwoxRzJvbGNiTm56blRIMG5OU1Bkdjd3UzV2MGo0OVBUWTlNc3NqNkpjSy92cmt0QnF1SlMydmR0a0VTOTR5R0VrCkdNQzdUcU1BR0tIenhqZUxRQWpuTTlNclVIamJ4ajhmS2wxc2lDOVdDNFVaKzB2Q1hKWmJnOXNBVDNXWjM2dkgKT3FQeEZlbzJ4L3hRNkFiQUh0WC9VaU5yVisvQXMrNVdQQTRtL1JmQW9NcGJFdjNsWXZORlRmNm5OcWJMaG9WdQprOEp3SlZjUHZjTmVieTFvaXVWRXppNGM2MVdsenVYSmN0VFh5ZmtiYTRWY0dIQjNiY0wzSzYydVZNZjZ2Ni9KClN1WWduN1ZrWHlvcFlPTUNKaFV2Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K", "certificate_ca": null, "verify_cert": true, "valid_from": "2025-08-14T01:03:39Z", "valid_to": "2035-08-12T01:03:39Z", "serial_number": "225201995349720307785813061472574617947173845747"}}]