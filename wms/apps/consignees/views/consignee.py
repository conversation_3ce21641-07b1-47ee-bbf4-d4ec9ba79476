import json
# from typing import Any

# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin
# from django.db.models import QuerySet
# from django.urls import reverse, reverse_lazy
# from django.utils.translation import gettext_lazy as _

# from actstream.models import Action
# from django_filters.views import FilterView
# from django_tables2 import SingleTableMixin

# from wss.cores.actstream import query_actstream
# from wss.cores.views import (
#     CoreBaseHistoryModifiedView,
#     CoreCreateView,
#     CoreDataTablesView,
#     CoreDeleteView,
#     CoreDetailDataTablesView,
#     CoreDetailView,
#     CoreListView,
#     CoreUpdateView,
# )

# from wss.apps.releases.models import WarehouseReleaseOrder

# from ..filters import ConsigneeFilter, ConsigneeHistoryFilter
# from ..forms import (
#     ConsigneeBillingAddressUpdateForm,
#     ConsigneeContactUpdateForm,
#     ConsigneeForm,
#     ConsigneeInfoUpdateForm,
#     ConsigneeShipping<PERSON>ddressUpdateForm,
# )
# from ..models import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>sign<PERSON>, Consignee<PERSON>ont<PERSON>, ShippingAddress
# from ..tables import (
#     ConsigneeDataTables,
#     ConsigneeDetailDataTables,
#     ConsigneeHistoryDataTables,
#     ConsigneeReleaseOrdersDataTables,
# )

from django.http import HttpResponse
from django.shortcuts import get_object_or_404, render, redirect
from django.urls import reverse
from django.views.decorators.http import require_POST

from wms.cores.mixins import ExportTableMixin
from wms.cores.views import CoreSingleTableView, CoreDataTableDetailView, CoreCreateView, CoreDetailView, CoreUpdateView

from wms.apps.releases.models import WarehouseReleaseOrder

from ..models import BillingAddress, Consignee, ConsigneeContact, ShippingAddress
from ..filters import ConsigneeFilter
from ..forms import ConsigneeForm, ConsigneeUpdateForm
from ..tables import ConsigneeTable, ConsigneeDetailTable


class ConsigneeCreateAndUpdateMixin:
    """Mixin for Create and Update."""

    model = Consignee
    template_name = "consignees/forms/create_or_update_form.html"


    def form_valid(self, form):
        self.object = form.save()

        if self.object.primary_contact:
            primary_contact = self.object.primary_contact
        else:
            primary_contact = ConsigneeContact(consignee=self.object)

        primary_contact.salutation = form.cleaned_data.get("salutation", "")
        primary_contact.first_name = form.cleaned_data.get("first_name", "")
        primary_contact.last_name = form.cleaned_data.get("last_name", "")
        primary_contact.email = form.cleaned_data.get("email", "")
        primary_contact.phone = form.cleaned_data.get("phone", "")
        primary_contact.designation = form.cleaned_data.get("designation", "")
        primary_contact.department = form.cleaned_data.get("department", "")
        primary_contact.is_primary = True
        primary_contact.save()

        if self.object.billing_address:
            billing_address = self.object.billing_address
        else:
            billing_address = BillingAddress(consignee=self.object)

        billing_address.address_attention = form.cleaned_data.get("billing_address_attention", "")
        billing_address.address_street_1 = form.cleaned_data.get("billing_address_street_1", "")
        billing_address.address_street_2 = form.cleaned_data.get("billing_address_street_2", "")
        billing_address.address_postal_code = form.cleaned_data.get("billing_address_postal_code", "")
        billing_address.address_district = form.cleaned_data.get("billing_address_district", "")
        billing_address.address_city = form.cleaned_data.get("billing_address_city", "")
        billing_address.address_state = form.cleaned_data.get("billing_address_state", "")
        billing_address.address_country = form.cleaned_data.get("billing_address_country", "")
        billing_address.address_phone = form.cleaned_data.get("billing_address_phone", "")
        billing_address.is_primary = True
        billing_address.save()

        if self.object.shipping_address:
            shipping_address = self.object.shipping_address
        else:
            shipping_address = ShippingAddress(consignee=self.object)

        shipping_address.address_attention = form.cleaned_data.get("shipping_address_attention", "")
        shipping_address.address_street_1 = form.cleaned_data.get("shipping_address_street_1", "")
        shipping_address.address_street_2 = form.cleaned_data.get("shipping_address_street_2", "")
        shipping_address.address_postal_code = form.cleaned_data.get("shipping_address_postal_code", "")
        shipping_address.address_district = form.cleaned_data.get("shipping_address_district", "")
        shipping_address.address_city = form.cleaned_data.get("shipping_address_city", "")
        shipping_address.address_state = form.cleaned_data.get("shipping_address_state", "")
        shipping_address.address_country = form.cleaned_data.get("shipping_address_country", "")
        shipping_address.address_phone = form.cleaned_data.get("shipping_address_phone", "")
        shipping_address.is_primary = True
        shipping_address.save()

        return super().form_valid(form)

    # def get_success_url(self):
    #     return reverse("consignees:detail", kwargs={"pk": self.object.pk})


class ConsigneeListView(ExportTableMixin, CoreSingleTableView):
    """
    View for displaying and managing the list of Consignees.
    Supports:
    - Pagination
    - Column visibility
    - Searching
    - Export functionality
    - HTMX partial updates
    """
    model = Consignee
    table_class = ConsigneeTable
    template_name = "consignees/mains/list.html"
    partial_template_name = "consignees/partials/table.html"  # Optional, for HTMX
    queryset = Consignee.objects.all()
    filterset_class = ConsigneeFilter

    # Search configuration
    search_fields = ["code", "company_name", "display_name"]

    # Export configuration
    export_name = "consignees"
    export_permission = []  # Empty list means no specific permissions required


class ConsigneeCreateView(ConsigneeCreateAndUpdateMixin, CoreCreateView):
    # model = Consignee
    form_class = ConsigneeForm
    template_name = "consignees/forms/create_or_update_form.html"
    success_url = "consignees:panel"
    section_title = "Create Consignee"
    section_desc = ""
    submit_text = "Save"
    cancel_url = "consignees:list"


class ConsigneeUpdateView(ConsigneeCreateAndUpdateMixin, CoreUpdateView):
    # model = Consignee
    form_class = ConsigneeUpdateForm
    template_name = "consignees/forms/create_or_update_form.html"
    success_url = "consignees:panel"
    section_title = "Update Consignee"
    section_desc = ""
    submit_text = "Update"
    cancel_url = "consignees:list"

    def get_initial(self):
        initial = super().get_initial()

        primary_contact = self.object.primary_contact
        if primary_contact:
            initial["salutation"] = primary_contact.salutation
            initial["first_name"] = primary_contact.first_name
            initial["last_name"] = primary_contact.last_name
            initial["email"] = primary_contact.email
            initial["phone"] = primary_contact.phone
            initial["designation"] = primary_contact.designation
            initial["department"] = primary_contact.department

        billing_address = self.object.billing_address
        if billing_address:
            initial["billing_address_attention"] = billing_address.address_attention
            initial["billing_address_street_1"] = billing_address.address_street_1
            initial["billing_address_street_2"] = billing_address.address_street_2
            initial["billing_address_postal_code"] = billing_address.address_postal_code
            initial["billing_address_district"] = billing_address.address_district
            initial["billing_address_city"] = billing_address.address_city
            initial["billing_address_state"] = billing_address.address_state
            initial["billing_address_country"] = billing_address.address_country
            initial["billing_address_phone"] = billing_address.address_phone

        shipping_address = self.object.shipping_address
        if shipping_address:
            initial["shipping_address_attention"] = shipping_address.address_attention
            initial["shipping_address_street_1"] = shipping_address.address_street_1
            initial["shipping_address_street_2"] = shipping_address.address_street_2
            initial["shipping_address_postal_code"] = shipping_address.address_postal_code
            initial["shipping_address_district"] = shipping_address.address_district
            initial["shipping_address_city"] = shipping_address.address_city
            initial["shipping_address_state"] = shipping_address.address_state
            initial["shipping_address_country"] = shipping_address.address_country
            initial["shipping_address_phone"] = shipping_address.address_phone

        return initial


class ConsigneeDetailHomeView(CoreDetailView):
    model = Consignee
    template_name = 'consignees/mains/home.html'


class ConsigneeDetailView(CoreDetailView):
    model = Consignee
    template_name = 'consignees/partials/detail.html'


class ConsigneeEventView(CoreDetailView):
    model = Consignee
    template_name = 'consignees/partials/event.html'


class ConsigneeDataTableDetailView(CoreDataTableDetailView):
    model = Consignee
    table_class = ConsigneeDetailTable
    context_object_name = "consignee"  # this is the default, but making it explicit
    partial_view = ConsigneeDetailHomeView
    search_fields = ["code"]


###########################################################################
# consignee delete
###########################################################################


def consignee_delete_form(request, pk):
    """
    Display the form for deleting a consignee.
    """
    consignee = get_object_or_404(Consignee, pk=pk)

    if WarehouseReleaseOrder.objects.filter(consignee=consignee).exists() is True:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Only Consignee without any WRO can be deleted.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request,
                       _("Only Consignee without any WRO can be deleted."))
        return HttpResponse(status=400, content="Consignee not eligible for deletion.")

    context = {
        'consignee': consignee,
        'request': request,
    }

    return render(request, 'consignees/partials/consignee_delete_form.html', context)


@require_POST
def consignee_delete(request, pk):
    """
    Delete a consignee.
    """
    consignee = get_object_or_404(Consignee, pk=pk)

    if WarehouseReleaseOrder.objects.filter(consignee=consignee).exists() is True:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Only Consignee without any WRO can be deleted.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request,
                       _("Only Consignee without any WRO can be deleted."))
        return HttpResponse(status=400, content="Consignee not eligible for deletion.")

    try:
        # Attempt to delete the consignee
        consignee.delete()

        # For HTMX requests, return success response
        if request.headers.get('HX-Request'):
            trigger_data = {
                "closeModalEvent": None,
                "showNotificationEvent": {
                    "message": "Consignee deleted successfully!",
                    "type": "success"
                },
                # Redirect to the list page after deletion
                "redirectEvent": {"url": reverse("consignees:list")}
            }

            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            return HttpResponse(status=200, headers=headers)

        # For non-HTMX requests, redirect to the list page
        messages.success(request, _("Consignee deleted successfully."))
        return redirect(reverse("consignees:list"))

    except Exception as e:
        # Handle validation errors or other exceptions
        error_message = str(e)

        # For HTMX requests, return error response
        if request.headers.get('HX-Request'):
            trigger_data = {
                "closeModalEvent": None,
                "showNotificationEvent": {
                    "message": f"Failed to delete consignee: {error_message}",
                    "type": "error"
                }
            }

            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            return HttpResponse(status=400, headers=headers)

        # For non-HTMX requests, redirect back with error message
        messages.error(request, _("Failed to delete consignee: %(error)s") % {'error': error_message})
        return redirect(reverse("consignees:panel", kwargs={"pk": pk}))


###########################################################################
# end consignee delete
###########################################################################



# class ConsigneeListView(LoginRequiredMixin, PermissionRequiredMixin, FilterView, SingleTableMixin, CoreListView):
#     """Page to show all Consignee. This page use DataTables server side."""

#     model = Consignee
#     template_name = "consignees/list.html"

#     table_class = ConsigneeDataTables
#     filterset_class = ConsigneeFilter

#     # To prevent query all object as it will use ajax call in template
#     object_list = Consignee.objects.none()

#     header_title = "Consignees"
#     selected_page = "consignees"
#     selected_subpage = None

#     permission_required = ("consignees.view_consignee",)

#     def get_queryset(self):
#         """To prevent query all object."""
#         return self.object_list


# consignee_list_view = ConsigneeListView.as_view()


# class ConsigneeDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailDataTablesView):
#     """Page to display selected Consignee based on given Consignee's slug.
#     This page use DataTables server side."""

#     model = Consignee
#     template_name = "consignees/detail.html"

#     table_class = ConsigneeDetailDataTables

#     header_title = "Consignees"
#     selected_page = "consignees"
#     selected_subpage = None

#     permission_required = ("consignees.view_consignee",)


# consignee_detail_view = ConsigneeDetailView.as_view()


# class ConsigneeCreateAndUpdateMixin:
#     """Mixin for Create and Update."""

#     model = Consignee
#     form_class = ConsigneeForm
#     template_name = "consignees/create_or_update.html"

#     selected_page = "consignees"
#     selected_subpage = None

#     def form_valid(self, form):
#         self.object = form.save()

#         if self.object.primary_contact:
#             primary_contact = self.object.primary_contact
#         else:
#             primary_contact = ConsigneeContact(consignee=self.object)

#         primary_contact.salutation = form.cleaned_data.get("salutation", "")
#         primary_contact.first_name = form.cleaned_data.get("first_name", "")
#         primary_contact.last_name = form.cleaned_data.get("last_name", "")
#         primary_contact.email = form.cleaned_data.get("email", "")
#         primary_contact.phone = form.cleaned_data.get("phone", "")
#         primary_contact.designation = form.cleaned_data.get("designation", "")
#         primary_contact.department = form.cleaned_data.get("department", "")
#         primary_contact.is_primary = True
#         primary_contact.save()

#         if self.object.billing_address:
#             billing_address = self.object.billing_address
#         else:
#             billing_address = BillingAddress(consignee=self.object)

#         billing_address.address_attention = form.cleaned_data.get("billing_address_attention", "")
#         billing_address.address_street_1 = form.cleaned_data.get("billing_address_street_1", "")
#         billing_address.address_street_2 = form.cleaned_data.get("billing_address_street_2", "")
#         billing_address.address_postal_code = form.cleaned_data.get("billing_address_postal_code", "")
#         billing_address.address_district = form.cleaned_data.get("billing_address_district", "")
#         billing_address.address_city = form.cleaned_data.get("billing_address_city", "")
#         billing_address.address_state = form.cleaned_data.get("billing_address_state", "")
#         billing_address.address_country = form.cleaned_data.get("billing_address_country", "")
#         billing_address.address_phone = form.cleaned_data.get("billing_address_phone", "")
#         billing_address.is_primary = True
#         billing_address.save()

#         if self.object.shipping_address:
#             shipping_address = self.object.shipping_address
#         else:
#             shipping_address = ShippingAddress(consignee=self.object)

#         shipping_address.address_attention = form.cleaned_data.get("shipping_address_attention", "")
#         shipping_address.address_street_1 = form.cleaned_data.get("shipping_address_street_1", "")
#         shipping_address.address_street_2 = form.cleaned_data.get("shipping_address_street_2", "")
#         shipping_address.address_postal_code = form.cleaned_data.get("shipping_address_postal_code", "")
#         shipping_address.address_district = form.cleaned_data.get("shipping_address_district", "")
#         shipping_address.address_city = form.cleaned_data.get("shipping_address_city", "")
#         shipping_address.address_state = form.cleaned_data.get("shipping_address_state", "")
#         shipping_address.address_country = form.cleaned_data.get("shipping_address_country", "")
#         shipping_address.address_phone = form.cleaned_data.get("shipping_address_phone", "")
#         shipping_address.is_primary = True
#         shipping_address.save()

#         return super().form_valid(form)

#     def get_success_url(self):
#         return reverse("consignees:detail", kwargs={"slug": self.object.slug})


# class ConsigneeCreateView(ConsigneeCreateAndUpdateMixin, LoginRequiredMixin, PermissionRequiredMixin, CoreCreateView):
#     """Page to create Consignee."""

#     success_message = _("Consignee %(display_name)s successfully created")

#     header_title = "New Consignee"

#     permission_required = ("consignees.add_consignee",)


# consignee_create_view = ConsigneeCreateView.as_view()


# class ConsigneeUpdateView(ConsigneeCreateAndUpdateMixin, LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Page to update selected Consignee based on given Consignee's slug."""

#     success_message = _("Consignee %(display_name)s successfully updated")

#     header_title = "Update Consignee"

#     permission_required = ("consignees.change_consignee",)

#     def get_initial(self):
#         initial = super().get_initial()

#         primary_contact = self.object.primary_contact
#         if primary_contact:
#             initial["salutation"] = primary_contact.salutation
#             initial["first_name"] = primary_contact.first_name
#             initial["last_name"] = primary_contact.last_name
#             initial["email"] = primary_contact.email
#             initial["phone"] = primary_contact.phone
#             initial["designation"] = primary_contact.designation
#             initial["department"] = primary_contact.department

#         billing_address = self.object.billing_address
#         if billing_address:
#             initial["billing_address_attention"] = billing_address.address_attention
#             initial["billing_address_street_1"] = billing_address.address_street_1
#             initial["billing_address_street_2"] = billing_address.address_street_2
#             initial["billing_address_postal_code"] = billing_address.address_postal_code
#             initial["billing_address_district"] = billing_address.address_district
#             initial["billing_address_city"] = billing_address.address_city
#             initial["billing_address_state"] = billing_address.address_state
#             initial["billing_address_country"] = billing_address.address_country
#             initial["billing_address_phone"] = billing_address.address_phone

#         shipping_address = self.object.shipping_address
#         if shipping_address:
#             initial["shipping_address_attention"] = shipping_address.address_attention
#             initial["shipping_address_street_1"] = shipping_address.address_street_1
#             initial["shipping_address_street_2"] = shipping_address.address_street_2
#             initial["shipping_address_postal_code"] = shipping_address.address_postal_code
#             initial["shipping_address_district"] = shipping_address.address_district
#             initial["shipping_address_city"] = shipping_address.address_city
#             initial["shipping_address_state"] = shipping_address.address_state
#             initial["shipping_address_country"] = shipping_address.address_country
#             initial["shipping_address_phone"] = shipping_address.address_phone

#         return initial


# consignee_update_view = ConsigneeUpdateView.as_view()


# class ConsigneeDeleteView(LoginRequiredMixin, PermissionRequiredMixin, CoreDeleteView):
#     """Page to delete selected Consignee based on given Consignee's slug."""

#     model = Consignee
#     success_url = reverse_lazy("consignees:list")
#     success_message = _("Consignee %(display_name)s successfully deleted")

#     permission_required = ("consignees.delete_consignee",)


# consignee_delete_view = ConsigneeDeleteView.as_view()


# ##################
# # FOR DataTables #
# ##################


# class ConsigneeDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in list page."""

#     model = Consignee
#     table_class = ConsigneeDataTables
#     filterset_class = ConsigneeFilter

#     permission_required = ("consignees.view_consignee",)

#     def get_queryset(self) -> QuerySet[Consignee]:
#         return self.model.objects.select_related("consignor").prefetch_related("consigneecontact_set").all()


# consignee_datatables_view = ConsigneeDataTablesView.as_view()


# class ConsigneeDetailDataTablesView(ConsigneeDataTablesView):
#     """JSON for DataTables in detail page."""

#     table_class = ConsigneeDetailDataTables


# consignee_detail_datatables_view = ConsigneeDetailDataTablesView.as_view()


# class ConsigneeHistoryDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in Consignee History's page."""

#     model = Action
#     table_class = ConsigneeHistoryDataTables
#     filterset_class = ConsigneeHistoryFilter

#     permission_required = "consignees.view_consignee"

#     def get_consignee_information(self):
#         self.consignee = Consignee.objects.prefetch_related(
#             "billingaddress_set", "shippingaddress_set", "consigneecontact_set"
#         ).get(slug=self.kwargs["slug"])

#         self.consignee_billingaddress = self.consignee.billingaddress_set.all()
#         self.consignee_shippingaddress = self.consignee.shippingaddress_set.all()
#         self.consignee_consigneecontact = self.consignee.consigneecontact_set.all()

#     def get_queryset(self) -> QuerySet[Action]:

#         self.get_consignee_information()
#         action_consignee_qs = query_actstream(target=self.consignee)

#         action_qs = action_consignee_qs

#         return action_qs


# consignee_history_datatables_view = ConsigneeHistoryDataTablesView.as_view()


# ############
# # FOR HTMX #
# ############


# class ConsigneeInfoDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to display selected Consignee's info page based on given Consignee's slug."""

#     model = Consignee
#     template_name = "consignees/partials/htmx/_info.html"

#     permission_required = ("consignees.view_consignee",)


# consignee_info_detail_view = ConsigneeInfoDetailView.as_view()


# class ConsigneeInfoUpdateView(LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Partial page to update selected Consignee's info page based on given Consignee's slug."""

#     model = Consignee
#     form_class = ConsigneeInfoUpdateForm
#     template_name = "consignees/partials/htmx/_info_form.html"
#     success_message = _("Consignee %(display_name)s basic information successfully updated")

#     permission_required = ("consignees.change_consignee",)

#     def get_success_url(self):
#         return reverse("consignees:info", kwargs={"slug": self.object.slug})


# consignee_info_update_view = ConsigneeInfoUpdateView.as_view()


# class ConsigneePrimaryContactDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to display selected Consignee's primary contact page based on given Consignee's slug."""

#     model = Consignee
#     template_name = "consignees/partials/htmx/_primary_contact.html"

#     permission_required = ("consignees.view_consignee",)


# consignee_primary_contact_detail_view = ConsigneePrimaryContactDetailView.as_view()


# class ConsigneePrimaryContactUpdateView(LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Partial page to update selected Consignee's primary contact page based on given Consignee's slug."""

#     model = ConsigneeContact
#     form_class = ConsigneeContactUpdateForm
#     template_name = "consignees/partials/htmx/_primary_contact_form.html"
#     success_message = _("Primary contact successfully updated")

#     permission_required = ("consignees.change_consignee",)

#     def get_object(self):
#         contact = ConsigneeContact.objects.filter(consignee__slug=self.kwargs["slug"], is_primary=True).first()
#         if not contact:
#             consignee = Consignee.objects.get(slug=self.kwargs["slug"])
#             contact = ConsigneeContact.objects.create(consignee=consignee, is_primary=True)
#         return contact

#     def get_success_url(self):
#         return reverse("consignees:primary_contact", kwargs={"slug": self.object.consignee.slug})


# consignee_primary_contact_update_view = ConsigneePrimaryContactUpdateView.as_view()


# class ConsigneeBillingAddressDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to display selected Consignee's billing address page based on given Consignee's slug."""

#     model = Consignee
#     template_name = "consignees/partials/htmx/_billing_address.html"

#     permission_required = ("consignees.view_consignee",)


# consignee_billing_address_detail_view = ConsigneeBillingAddressDetailView.as_view()


# class ConsigneeBillingAddressUpdateView(LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Partial page to update selected Consignee's billing address page based on given Consignee's slug."""

#     model = BillingAddress
#     form_class = ConsigneeBillingAddressUpdateForm
#     template_name = "consignees/partials/htmx/_billing_address_form.html"
#     success_message = _("Billing address successfully updated")

#     permission_required = ("consignees.change_consignee",)

#     def get_object(self):
#         address = BillingAddress.objects.filter(consignee__slug=self.kwargs["slug"], is_primary=True).first()
#         if not address:
#             consignee = Consignee.objects.get(slug=self.kwargs["slug"])
#             address = BillingAddress.objects.create(consignee=consignee, is_primary=True)
#         return address

#     def get_success_url(self):
#         return reverse("consignees:billing_address", kwargs={"slug": self.object.consignee.slug})


# consignee_billing_address_update_view = ConsigneeBillingAddressUpdateView.as_view()


# class ConsigneeShippingAddressDetailView(LoginRequiredMixin, PermissionRequiredMixin, CoreDetailView):
#     """Partial page to display selected Consignee's shipping address page based on given Consignee's slug."""

#     model = Consignee
#     template_name = "consignees/partials/htmx/_shipping_address.html"

#     permission_required = ("consignees.view_consignee",)


# consignee_shipping_address_detail_view = ConsigneeShippingAddressDetailView.as_view()


# class ConsigneeShippingAddressUpdateView(LoginRequiredMixin, PermissionRequiredMixin, CoreUpdateView):
#     """Partial page to update selected Consignee's shipping address page based on given Consignee's slug."""

#     model = ShippingAddress
#     form_class = ConsigneeShippingAddressUpdateForm
#     template_name = "consignees/partials/htmx/_shipping_address_form.html"
#     success_message = _("Shipping address successfully updated")

#     permission_required = ("consignees.change_consignee",)

#     def get_object(self):
#         address = ShippingAddress.objects.filter(consignee__slug=self.kwargs["slug"], is_primary=True).first()
#         if not address:
#             consignee = Consignee.objects.get(slug=self.kwargs["slug"])
#             address = ShippingAddress.objects.create(consignee=consignee, is_primary=True)
#         return address

#     def get_success_url(self):
#         return reverse("consignees:shipping_address", kwargs={"slug": self.object.consignee.slug})


# consignee_shipping_address_update_view = ConsigneeShippingAddressUpdateView.as_view()


# class ConsigneeHistoryListView(LoginRequiredMixin, PermissionRequiredMixin, SingleTableMixin, CoreListView):
#     """
#     Partial page to show all Actstreams based on given Consignee's slug as target_object_id.
#     This page use DataTables server side.
#     """

#     model = Action
#     table_class = ConsigneeHistoryDataTables
#     template_name = "consignees/partials/htmx/_history.html"

#     # To prevent query all object as it will use ajax call in template
#     object_list = Action.objects.none()

#     permission_required = ("consignees.view_consignee",)

#     def get_queryset(self):
#         """To prevent query all object."""
#         return self.object_list

#     def get_consignee(self):
#         return Consignee.objects.get(slug=self.kwargs["slug"])

#     def get_context_data(self, **kwargs) -> Any:
#         context = super().get_context_data(**kwargs)
#         context["object"] = self.get_consignee()
#         return context


# consignee_history_list_view = ConsigneeHistoryListView.as_view()


# class ConsigneeHistoryModifiedView(CoreBaseHistoryModifiedView):
#     """
#     Partial pop up view to show the differences in Consignee history view.
#     """

#     permission_required = ("consignees.view_consignee",)


# consignee_history_modified_view = ConsigneeHistoryModifiedView.as_view()


# class ConsigneeReleaseOrdersListView(LoginRequiredMixin, PermissionRequiredMixin, SingleTableMixin, CoreListView):
#     """Partial page to show all warehouse release order belonging to a Consignee."""

#     model = WarehouseReleaseOrder
#     table_class = ConsigneeReleaseOrdersDataTables
#     template_name = "consignees/partials/htmx/_release_orders.html"

#     permission_required = ("releases.view_warehousereleaseorder",)

#     def get_queryset(self) -> QuerySet[WarehouseReleaseOrder]:
#         return self.model.objects.filter(consignee__slug=self.kwargs["slug"])

#     def get_total_releases_count(self) -> int:
#         return self.object_list.count()

#     def get_context_data(self, **kwargs) -> dict[str, Any]:
#         context = super().get_context_data(**kwargs)
#         context["total_releases_count"] = self.get_total_releases_count()
#         return context


# consignee_release_orders_list_view = ConsigneeReleaseOrdersListView.as_view()
