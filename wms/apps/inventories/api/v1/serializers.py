from rest_framework import serializers
from django.db.models import Q
from wms.apps.inventories.models import Item, Stock


class ItemSerializer(serializers.ModelSerializer):
    """
    Serializer for items model only.
    """
    id = serializers.SerializerMethodField()
    name = serializers.SerializerMethodField()
    uom = serializers.CharField(source='uom.symbol', read_only=True)
    uom_id = serializers.IntegerField(source='uom.id')

    class Meta:
        model = Item
        fields = ['id', 'name', 'uom', 'uom_id']

    def get_id(self, obj):
        """Return the item ID instead of the stock ID"""
        return obj.id

    def get_name(self, obj):
        """Return item name and code in format: "[code] name" """
        return f"[{obj.code}] {obj.name}"


class StockDetailSerializer(serializers.ModelSerializer):
    """Serializer for individual stock entries with specific expiry dates"""
    expiry_date = serializers.DateField(format="%Y-%m-%d", allow_null=True)
    available_balance = serializers.SerializerMethodField()

    class Meta:
        model = Stock
        fields = ['id', 'expiry_date', 'available_balance']

    def get_available_balance(self, obj):
        # Use pre-calculated balance if available (from optimized queryset)
        if hasattr(obj, 'calculated_available_balance'):
            return obj.calculated_available_balance
        # Fallback to cached property for backward compatibility
        return obj.available_balance

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if representation['expiry_date'] is None:
            representation['expiry_date'] = "N/A"
        return representation


class BatchSerializer(serializers.Serializer):
    """Serializer for batch information grouping stocks by batch number"""
    batch_no = serializers.CharField()
    stocks = serializers.SerializerMethodField()

    def get_stocks(self, obj):
        """Return all stocks with this batch number"""
        stocks = obj.get('stocks', [])
        return StockDetailSerializer(stocks, many=True).data


class ItemDetailSerializer(serializers.ModelSerializer):
    """Serializer for items with detailed batch and stock information."""
    id = serializers.SerializerMethodField()
    name = serializers.SerializerMethodField()
    uom = serializers.CharField(source='uom.symbol', read_only=True)
    uom_id = serializers.IntegerField(source='uom.id')
    batches = serializers.SerializerMethodField()

    class Meta:
        model = Item
        fields = ['id', 'name', 'uom', 'uom_id', 'batches']

    def get_id(self, obj):
        """Return the item ID"""
        return obj.id

    def get_name(self, obj):
        """Return item name and code in format: "[code] name" """
        return f"[{obj.code}] {obj.name}"

    def get_batches(self, obj):
        """Return all available batches for this item with stock information"""
        # Use prefetched stocks if available (from optimized queryset)
        if hasattr(obj, '_prefetched_objects_cache') and 'stock_set' in obj._prefetched_objects_cache:
            stocks = obj._prefetched_objects_cache['stock_set']
        else:
            # Fallback to database query with optimization
            stocks = obj.stock_set.select_related('item', 'item__uom', 'warehouse').order_by('batch_no', 'expiry_date')

        # Convert to list if it's a queryset for consistent handling
        if hasattr(stocks, '__iter__') and not isinstance(stocks, list):
            stocks = list(stocks)

        # If no stocks, return an empty list
        if not stocks:
            return []

        # Group stocks by batch_no
        batch_groups = {}
        for stock in stocks:
            batch_no = stock.batch_no if stock.batch_no else 'N/A'

            if batch_no not in batch_groups:
                batch_groups[batch_no] = {
                    'batch_no': batch_no,
                    'stocks': []
                }

            # Add stock to the batch group
            batch_groups[batch_no]['stocks'].append(stock)

        # Serialize the batch information
        serializer = BatchSerializer(batch_groups.values(), many=True)
        return serializer.data
