# from collections import defaultdict

from rest_framework import viewsets, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.shortcuts import get_object_or_404
from wms.cores.views import Select2Pagination

from wms.apps.inventories.models import Item
from .serializers import ItemSerializer, ItemDetailSerializer


class ItemViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Item objects in the Warehouse Management System.
    """
    pagination_class = Select2Pagination
    queryset = Item.objects.all().select_related('uom', 'consignor') \
        .prefetch_related('categories', 'warehouses', 'outbound_uom_display_conversions', 'stock_set')
    serializer_class = ItemDetailSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['code', 'name', 'item_type', 'status', 'uom', 'manage_type', 'consignor']
    search_fields = ['code', 'name', 'brand', 'sku', 'barcode']
    ordering_fields = ['code', 'name', 'sort_order', 'created']
    ordering = ['sort_order']

    @action(detail=True, methods=['get'])
    def detail(self, request, pk=None):
        """
        Get detailed information about a specific item by ID.
        This endpoint is provided for API consistency, but returns the same data
        as the retrieve endpoint since we're using ItemDetailSerializer as the default.

        Parameters:
        - pk: Item ID

        Returns:
        - Detailed item information including batches, expiry dates, and available balances
        """
        # Get the item
        item = get_object_or_404(Item, pk=pk)

        # Serialize and return the item with detailed information
        serializer = self.get_serializer(item)
        return Response(serializer.data)
