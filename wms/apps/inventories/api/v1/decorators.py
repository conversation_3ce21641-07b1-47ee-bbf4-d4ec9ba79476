"""
Performance monitoring decorators for API endpoints.
"""
import time
import logging
from functools import wraps
from django.db import connection
from django.conf import settings

logger = logging.getLogger(__name__)


def monitor_performance(func):
    """
    Decorator to monitor API endpoint performance.
    Logs execution time and database query count.
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        if not getattr(settings, 'MONITOR_API_PERFORMANCE', False):
            return func(*args, **kwargs)
        
        start_time = time.time()
        initial_queries = len(connection.queries)
        
        try:
            result = func(*args, **kwargs)
            
            end_time = time.time()
            execution_time = end_time - start_time
            query_count = len(connection.queries) - initial_queries
            
            # Log performance metrics
            logger.info(
                f"API Performance - {func.__name__}: "
                f"Time: {execution_time:.3f}s, "
                f"Queries: {query_count}"
            )
            
            # Log slow queries (> 1 second)
            if execution_time > 1.0:
                logger.warning(
                    f"Slow API endpoint detected - {func.__name__}: "
                    f"Time: {execution_time:.3f}s, "
                    f"Queries: {query_count}"
                )
            
            return result
            
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            query_count = len(connection.queries) - initial_queries
            
            logger.error(
                f"API Error - {func.__name__}: "
                f"Time: {execution_time:.3f}s, "
                f"Queries: {query_count}, "
                f"Error: {str(e)}"
            )
            raise
    
    return wrapper


def cache_invalidation_key(model_name, instance_id):
    """
    Generate cache invalidation key for model instances.
    """
    return f"{model_name}_cache_invalidation_{instance_id}"


def invalidate_item_cache(item_id):
    """
    Invalidate cache for a specific item when stock changes.
    """
    from django.core.cache import cache
    
    # Pattern for item detail cache keys
    cache_pattern = f"item_detail_{item_id}_*"
    
    # In production, you might want to use a more sophisticated cache invalidation
    # For now, we'll use a simple approach
    cache.delete_many([cache_pattern])
