# from decimal import Decimal
# from typing import Union

# from django.db.models import Q, Sum

# from wss.apps.inventories.models import Stock
# from wss.apps.settings.models import UnitOfMeasure
# from wss.apps.settings.utils import uom_converter


# from decimal import Decimal
# from django.db import transaction
# from django.db.models import OuterRef, Subquery, Sum, Value
# from django.db.models.functions import Coalesce
#
# from wms.cores.utils import localtime_now
#
#
# # **Batch process updates**
# def batches(iterable, size):
#     """Helper function to yield batches of size `size`."""
#     for i in range(0, len(iterable), size):
#         yield iterable[i : i + size]
#
#
# def bulk_update_daily_stock_balances(stock_qs, batch_size=1000):
#     """Efficiently update or create DailyStockBalance records using Subqueries.
#
#     *NOTE: temporary commented out create first, reason being,
#            DailyStockBalance should haven been created WHENEVER RIGHT AFTER
#            a "stock + it's first transaction" being created.
#     """
#     # Validation first.
#     # Get all stock IDs from stock_qs
#     stock_ids = stock_qs.values_list("id", flat=True)
#
#     # Get stock IDs that have a DailyStockBalance linked
#     stocks_with_balance_obj = DailyStockBalance.objects.filter(
#         stock_id__in=stock_ids
#     ).values_list("stock_id", flat=True)
#
#     # Find missing stock IDs
#     missing_stocks = set(stock_ids) - set(stocks_with_balance_obj)
#
#     if missing_stocks:
#         # Fetch stock objects for detailed error message
#         missing_stock_objs = stock_qs.filter(id__in=missing_stocks)
#         raise ValidationError(
#             f"The following stocks are missing DailyStockBalance entries: {list(missing_stock_objs)}"
#         )
#
#     # Validated, start processing
#     # Subquery to get the latest transaction for each stock
#     latest_transaction_subquery = (
#         Transaction.objects.filter(stock=OuterRef("id"))
#         .order_by("-transaction_datetime")
#         .values("id")[:1]
#     )
#
#     # Subquery to get the sum of system_quantity for each stock
#     total_balance_subquery = (
#         Transaction.objects.filter(stock=OuterRef("id"))
#         .values("stock")
#         .annotate(total_balance=Coalesce(Sum("system_quantity"), Value(Decimal("0"))))
#         .values("total_balance")[:1]
#     )
#
#     # Bulk update DailyStockBalance for existing records
#     existing_balances = DailyStockBalance.objects.filter(stock__in=stock_qs).annotate(
#         latest_transaction=Subquery(latest_transaction_subquery),
#         latest_balance=Subquery(total_balance_subquery),
#     )
#
#     to_update = []
#     # to_create = []
#
#     for daily_balance_obj in existing_balances:
#         if daily_balance_obj.latest_transaction:
#             daily_balance_obj.transaction_state_id = daily_balance_obj.latest_transaction
#             daily_balance_obj.balance = daily_balance_obj.latest_balance
#             daily_balance_obj.recalculate_datetime = localtime_now()
#             to_update.append(daily_balance_obj)
#
#     # Perform bulk updates and bulk creates within a transaction
#     with transaction.atomic():
#         if to_update:
#             for batch_update in batches(to_update, batch_size):
#                 DailyStockBalance.objects.bulk_update(
#                     batch_update,
#                     ["transaction_state", "balance", "recalculate_datetime"]
#                 )
#             return len(to_update)
#         else:
#             return 0
#         # if to_create:
#         #     DailyStockBalance.objects.bulk_create(to_create)
#
#
# def single_create_or_update_daily_stock_balances(stock, transaction):
#     """
#     create or update single DailyStockBalance records by assessing
#     whether is it a backdate case or a new obj case.
#     Skip if it's normal future new transaction being created, balance should able to just perform addition sum
#     """
#     # backdate transaction before dailystockbalance's recalculate_datetime
#     if stock.dailystockbalance and transaction.transaction_datetime < stock.dailystockbalance.recalculate_datetime:
#         daily_stock_balance = DailyStockBalance.objects.get(
#             stock=stock,
#             # transaction_state=transaction,
#             # balance=transaction.system_quantity,
#             # recalculate_datetime = localtime_now()
#         )
#         latest_transaction = Transaction.objects.filter(stock=stock).order_by("-transaction_datetime")[:1]
#         latest_total_balance = Transaction.objects.filter(
#             stock=stock,
#             transaction_datetime__lte=latest_transaction.transaction_datetime
#         ).aggregate(
#             Sum("system_quantity")
#         ).get("system_quantity__sum", Decimal("0")) or Decimal("0")
#
#         daily_stock_balance
#         daily_stock_balance.transaction_state = latest_transaction
#         daily_stock_balance.balance = latest_total_balance
#         daily_stock_balance.recalculate_datetime = localtime_now()
#         daily_stock_balance.save()
#
#     # create new dailystockbalance
#     elif stock.dailystockbalance is None:
#         DailyStockBalance.objects.create(
#             stock=stock,
#             transaction_state=transaction,
#             balance=transaction.system_quantity,
#             recalculate_datetime = localtime_now()
#         )
#
#
# def get_reserved_rack_list(release_order_item):
#     """
#     Return a list of dictionaries containing the reserved racks and their quantities.
#     """
#     reserved_rack_list = []

#     rack_transaction_qs = release_order_item.reserved_rack_transactions
#     for rack_transaction in rack_transaction_qs:
#         rack = rack_transaction.rackstorage.rack
#         reserved_quantity = rack_transaction.reserved_quantity
#         picked_quantity = rack_transaction.warehouse_release_order_item.get_picked_reserved_quantity(racks=rack)
#         remaining_quantity = reserved_quantity - picked_quantity
#         fully_picked = remaining_quantity <= 0

#         reserved_rack_list.append(
#             {
#                 "rack_transaction": rack_transaction,
#                 "rack": rack_transaction.rackstorage.rack,
#                 "reserved_quantity": f"{round(reserved_quantity, release_order_item.item.uom.unit_precision)}",
#                 "picked_quantity": f"{round(picked_quantity, release_order_item.item.uom.unit_precision)}",
#                 "remaining_quantity": f"{round(remaining_quantity, release_order_item.item.uom.unit_precision)}",
#                 "fully_picked": fully_picked,
#                 "item_uom": release_order_item.item.uom.symbol,
#                 "item_code": release_order_item.item.code,
#             }
#         )

#     return reserved_rack_list


# def get_warehouse_stock_list(item, filter_type="all", batch_no=None, expiry_date=None, wro=None):
#     """
#     type options:
#     "all", "defect", "on_hand"

#     Return:
#     [
#         {
#             "warehouse": WarehouseObj,
#             "warehouse_stock_obj": StockObj,
#             "stock_total_balance": 15.0,
#             "filtered_stock_with_item_warehouse_qs": QuerySet(),
#         },
#         {
#             "warehouse": WarehouseObj,
#             "warehouse_stock_obj": StockObj,
#             "stock_total_balance": 15.0,
#             "filtered_stock_with_item_warehouse_qs": QuerySet(),
#         },
#     ]
#     """

#     prepared_list = []
#     if filter_type == "on_hand":
#         stock_qs = Stock.objects.filter(item=item).exclude(warehouse__name__icontains="defect")
#     elif filter_type == "defect":
#         stock_qs = Stock.objects.filter(item=item, warehouse__name__icontains="defect")
#     else:
#         stock_qs = Stock.objects.filter(item=item)

#     if batch_no is not None:
#         stock_qs = stock_qs.filter(batch_no=batch_no)

#     if expiry_date is not None:
#         stock_qs = stock_qs.filter(expiry_date=expiry_date)

#     involved_warehouses_stock_qs = stock_qs.order_by("warehouse__path").distinct("warehouse__path")

#     for warehouses_stock in involved_warehouses_stock_qs:
#         temp_dict = {}

#         filtered_stock_with_item_warehouse_qs = stock_qs.filter(warehouse=warehouses_stock.warehouse)
#         stock_total_balance = filtered_stock_with_item_warehouse_qs.aggregate(Sum("balance")).get(
#             "balance__sum", Decimal("0")
#         )

#         is_warehouse_involved_in_wro = False
#         if wro and warehouses_stock.warehouse in wro.warehouses.all():
#             is_warehouse_involved_in_wro = True

#         temp_dict = {
#             "warehouse": warehouses_stock.warehouse,
#             "warehouse_parent": warehouses_stock.warehouse.get_parent(),
#             "warehouse_stock_obj": warehouses_stock,
#             "stock_total_balance": round(stock_total_balance, item.uom.unit_precision),
#             "item_uom": item.uom,
#             "filtered_stock_with_item_warehouse_qs": filtered_stock_with_item_warehouse_qs,
#             "is_warehouse_involved_in_wro": is_warehouse_involved_in_wro,
#         }
#         prepared_list.append(temp_dict)

#     return prepared_list


# def get_total_qty_selected_warehouses(warehouses, target_uom: Union[UnitOfMeasure, None] = None) -> Decimal:
#     """
#     Given selected warehouses as argument, returns the total quantities.
#     """

#     total_quantity = Stock.objects.filter(warehouse__in=warehouses, warehouse__is_storage=True).aggregate(
#         Sum("balance")
#     ).get("balance__sum", Decimal("0")) or Decimal("0")

#     if target_uom and isinstance(target_uom, UnitOfMeasure):
#         default_uom = UnitOfMeasure.objects.get(symbol="EA")
#         total_quantity = uom_converter(
#             origin_uom=default_uom,
#             target_uom=target_uom,
#             quantity=total_quantity,
#         )

#     return total_quantity


# def get_total_qty_by_warehouses_type(
#     filter_type: str = "all", target_uom: Union[UnitOfMeasure, None] = None
# ) -> Decimal:
#     """
#     Returns the total quantities by warehouse's type.

#     type options:
#     "all", "defect", "on_hand", "on_hold"
#     """
#     if filter_type == "on_hand":
#         stock_qs = Stock.objects.exclude(
#             Q(warehouse__name__icontains="defect") | Q(warehouse__name__icontains="on hold"), warehouse__is_storage=True
#         )
#     elif filter_type == "defect":
#         stock_qs = Stock.objects.filter(warehouse__name__icontains="defect", warehouse__is_storage=True)
#     elif filter_type == "on_hold":
#         stock_qs = Stock.objects.filter(warehouse__name__icontains="on hold", warehouse__is_storage=True)
#     else:
#         stock_qs = Stock.objects.all()

#     total_quantity = stock_qs.aggregate(Sum("balance")).get("balance__sum", Decimal("0")) or Decimal("0")

#     if target_uom and isinstance(target_uom, UnitOfMeasure):
#         default_uom = UnitOfMeasure.objects.get(symbol="EA")
#         total_quantity = uom_converter(
#             origin_uom=default_uom,
#             target_uom=target_uom,
#             quantity=total_quantity,
#         )

#     return total_quantity
