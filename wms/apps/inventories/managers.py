from decimal import Decimal

from django.db import models
from django.db.models import Sum, Q, F, Value, CharField, OuterRef, Subquery
from django.db.models.functions import Coalesce, Concat


from wms.cores.utils import normalize_decimal

# from wss.apps.settings.utils import uom_converter


class TransactionManager(models.Manager):
    """Manager for Transaction model."""

    def balance_by_warehouse_item(self, warehouse, item):
        """Return balance of given warehouse, item."""
        balance = super().get_queryset().select_related(
            "stock",
            "stock__warehouse",
            "stock__item",
            "uom",
        ).filter(stock__warehouse=warehouse, stock__item=item).aggregate(
            Sum("system_quantity")
        ).get("system_quantity__sum", Decimal("0")) or Decimal("0")

        return balance

    def balance_by_item(self, item):
        """Return balance of given item."""
        balance = super().get_queryset().select_related(
            "stock",
            "stock__warehouse",
            "stock__item",
            "uom",
        ).filter(stock__item=item).aggregate(Sum("system_quantity")).get(
            "system_quantity__sum", Decimal("0")
        ) or Decimal("0")
        return balance.normalize()

    def balance_by_item_batch_no(self, item, batch_no):
        """Return balance of given item and batch_no."""
        balance = super().get_queryset().select_related(
            "stock",
            "stock__warehouse",
            "stock__item",
            "uom",
        ).filter(stock__item=item, stock__batch_no=batch_no).aggregate(
            Sum("system_quantity")
        ).get("system_quantity__sum", Decimal("0")) or Decimal("0")

        return balance

    def balance_by_stock(self, stock):
        """Return balance of given stock. WITHOUT consideration of reserved quantity"""
        balance = super().get_queryset().select_related(
            "stock",
            "stock__warehouse",
            "stock__item",
            "uom",
        ).filter(stock=stock).aggregate(Sum("system_quantity")).get(
            "system_quantity__sum", Decimal("0")
        ) or Decimal("0")

        return normalize_decimal(balance)

    def sum_group_warehouse_by_item(self, item, defect=None):
        """Return sum of system_quantity grouped by warehouse for a given item.

        Args:
            item: Item instance to filter transactions
            defect: Filter warehouse by defect status
                   True - exclude warehouses containing 'defect'
                   False - include only warehouses containing 'defect'
                   None - include all warehouses (default)

        Returns:
            QuerySet: Contains warehouse and its total system_quantity, with Decimal('0') for empty values
                - warehouse_id: ID of the warehouse
                - warehouse_name: Name of the warehouse
                - parent_name: Name of the parent warehouse (if exists)
                - full_warehouse_path: Full hierarchical path of warehouse
                - total_quantity: Sum of system_quantity
                - uom_name: Unit of measure name

        Performance Optimizations:
        - Leverages database indexes (requires index on stock__warehouse__name)
        """

        from wms.apps.settings.models import Warehouse
        from wms.apps.inventories.models import Stock

        queryset = (
            super()
            .get_queryset()
            .filter(stock__item=item)
            .select_related(
                'stock',
                'stock__warehouse',
                'stock__warehouse__parent',
                'stock__item__uom'
            )
        )

        # Apply defect filtering using Q objects
        if defect is not None:
            defect_filter = Q(stock__warehouse__name__icontains='defect')
            queryset = queryset.exclude(defect_filter) if defect else queryset.filter(defect_filter)

        warehouse_sums = (
            queryset
            .values(
                'stock__warehouse',
            )
            .annotate(
                warehouse_id=F('stock__warehouse'),
                warehouse_name=F('stock__warehouse__name'),
                total_quantity=Coalesce(Sum('system_quantity'), Decimal('0')),
                uom_symbol=F('stock__item__uom__symbol')
            )
            .order_by('stock__warehouse__path')
        )

        results = []
        for warehouse in warehouse_sums:
            # Get the warehouse instance to use its methods
            w = Warehouse.objects.get(id=warehouse['warehouse_id'])
            # Use the warehouse's full_name method
            full_path = w.full_name.split('>')
            # include latest_stock in the list of dict
            latest_stock = Stock.objects.filter(warehouse=w, item=item).last()

            warehouse['warehouse_group'] = full_path[0]
            warehouse['full_warehouse_path'] = full_path[1:]
            warehouse['latest_stock'] = latest_stock
            results.append(warehouse)

        return results

    def stock_total_by_item(self, item):
        """
        Return a dictionary containing the total stock quantities for an item,
        separated by defect and non-defect warehouses.

        Args:
            item: The Item instance to get stock totals for

        Returns:
            dict: A dictionary with the following keys:
                - total_non_defect: Sum of quantities in non-defect warehouses
                - total_defect: Sum of quantities in defect warehouses
                - total: Total quantity across all warehouses
        """
        from decimal import Decimal

        # Filter for defect and non-defect warehouses
        defect_filter = Q(stock__warehouse__name__icontains='defect')

        # Get total for non-defect warehouses
        total_non_defect = super().get_queryset().filter(
            stock__item=item
        ).exclude(
            defect_filter
        ).aggregate(
            Sum("system_quantity")
        ).get("system_quantity__sum", Decimal("0")) or Decimal("0")

        # Get total for defect warehouses
        total_defect = super().get_queryset().filter(
            stock__item=item,
        ).filter(
            defect_filter
        ).aggregate(
            Sum("system_quantity")
        ).get("system_quantity__sum", Decimal("0")) or Decimal("0")

        # Calculate the total
        total = total_non_defect + total_defect

        # Get the UOM symbol from the item
        uom_symbol = item.uom.symbol if item.uom else ""
        return {
            "total_non_defect": total_non_defect,
            "total_defect": total_defect,
            "total": total,
            "uom_symbol": uom_symbol,
        }

    def total_in_by_stock(self, stock):
        """Return total_in of given stock."""
        total_in = super().get_queryset().filter(stock=stock, system_quantity__gte=0).aggregate(
            Sum("system_quantity")
        ).get("system_quantity__sum", Decimal("0")) or Decimal("0")

        return normalize_decimal(total_in)

    def total_out_by_stock(self, stock):
        """Return total_out of given stock."""

        total_out = super().get_queryset().filter(stock=stock, quantity__lt=0).aggregate(Sum("system_quantity")).get(
            "system_quantity__sum", Decimal("0")
        ) or Decimal("0")

        return normalize_decimal(total_out)

#     def recalculate_balance_by_stock(self, stock):
#         """Recalculate balance of given stock."""
#         transactions = super().get_queryset().filter(stock=stock)

#         if transactions.exists():
#             for transaction in transactions:

#                 converted_quantity = uom_converter(
#                     origin_uom=transaction.uom,
#                     target_uom=transaction.stock.item.uom,
#                     quantity=transaction.quantity,
#                     skip_unit_precision=True,
#                 )

#                 new_balance = transaction.get_previous_transactions_balance + converted_quantity
#                 transaction.balance = new_balance
#                 transaction.save(update_fields=["balance"], skip_balance=True)

#     def get_total_quantity(self, warehouse=None, target_uom: (UnitOfMeasure, None) = None):
#         """Get the total quantity for Transaction based on provided warehouse."""
#         total_quantity = super().get_queryset().filter(
#             stock__warehouse=warehouse, stock__warehouse__is_storage=True
#         ).aggregate(Sum("system_quantity")).get("system_quantity__sum", Decimal("0")) or Decimal("0")

#         if target_uom and isinstance(target_uom, UnitOfMeasure):
#             default_uom = UnitOfMeasure.objects.get(symbol="EA")
#             total_quantity = uom_converter(
#                 origin_uom=default_uom,
#                 target_uom=target_uom,
#                 quantity=total_quantity,
#             )
#         return total_quantity


class ReservedTransactionManager(models.Manager):
    """Manager for ReservedTransaction model."""

    def balance_by_warehouse_item(self, warehouse, item):
        """Return balance of given warehouse, item."""
        balance = super().get_queryset().filter(
            reserved_stock__stock__warehouse=warehouse,
            reserved_stock__stock__item=item
        ).aggregate(
            Sum("system_quantity")
        ).get("system_quantity__sum", Decimal("0")) or Decimal("0")

        return balance

    def current_total_reserved_amount_by_stock(self, stock):
        """Return current total reserved amount of given stock."""
        from wms.apps.inventories.models import ReservedTransaction

        balance = super().get_queryset().filter(
            status=ReservedTransaction.Status.RESERVED,
            reserved_stock__stock=stock,
        ).aggregate(Sum("system_quantity")).get(
            "system_quantity__sum", Decimal("0")
        ) or Decimal("0")

        return balance


class StockManager(models.Manager):
    """Manager for Stock model."""

    def update_expiry_date(self, item=None, batch_no="", expiry_date=None):
        """to update the standardize expiry date of all the items with same batch number but in different warehouses."""

        stock_qs = (
            super()
            .get_queryset()
            .filter(
                item=item,
                batch_no=batch_no,
            )
        )
        if expiry_date:
            stock_qs.update(expiry_date=expiry_date)
        else:
            if stock_qs.last().expiry_date:
                stock_qs.update(expiry_date=stock_qs.last().expiry_date)

    def with_calculated_balances(self, queryset=None):
        """
        Optimize stock queryset by pre-calculating balances to avoid N+1 queries.

        This method adds annotations for db_balance, reserved_amount, and available_balance
        to avoid individual database queries for each stock.
        """
        from django.db.models import Sum, Subquery, OuterRef
        from django.db.models.functions import Coalesce
        from wms.apps.inventories.models import Transaction, ReservedTransaction

        if queryset is None:
            queryset = self.get_queryset()

        # Subquery for db_balance (sum of all transactions for each stock)
        db_balance_subquery = Transaction.objects.filter(
            stock=OuterRef('pk')
        ).aggregate(
            total=Coalesce(Sum('system_quantity'), 0)
        )['total']

        # Subquery for reserved amount (sum of reserved transactions for each stock)
        reserved_amount_subquery = ReservedTransaction.objects.filter(
            status=ReservedTransaction.Status.RESERVED,
            reserved_stock__stock=OuterRef('pk')
        ).aggregate(
            total=Coalesce(Sum('system_quantity'), 0)
        )['total']

        return queryset.annotate(
            calculated_db_balance=Subquery(db_balance_subquery),
            calculated_reserved_amount=Subquery(reserved_amount_subquery),
            calculated_available_balance=models.F('calculated_db_balance') - models.F('calculated_reserved_amount')
        )
