# from django.contrib.auth.mixins import LoginRequiredMixin, PermissionRequiredMixin

# from django_filters.views import FilterView
# from django_tables2 import SingleTableMixin

# from wss.cores.views import CoreDataTablesView, CoreDetailView, CoreListView

# from ..filters import StockFilter, StockTransactionFilter
# from ..models import Stock, Transaction
# from ..tables import StockSideTable, StockTransactionDataTables

from django.db.models import QuerySet
from django.urls import reverse_lazy, reverse

from wms.apps.inventories.filters import (
    StockDataTableFilter,
    TransactionFilter,
)
from wms.apps.inventories.tables import (
    StockDetailTable,
    StockTransactionTable
)

from wms.cores.mixins import ExportTableMixin

from wms.cores.views import CoreSingleTableView, CoreDataTableDetailView, CoreCreateView, CoreDetailView, CoreUpdateView

from wms.apps.inventories.models import Item, Transaction, Stock


class StockTransactionListView(ExportTableMixin, CoreSingleTableView):
    """
    View for displaying and managing the list of Items.
    Supports:
    - Pagination
    - Column visibility
    - Searching
    - Export functionality
    - HTMX partial updates
    """
    model = Transaction
    table_class = StockTransactionTable
    template_name = "inventories/stocks/partials/transaction_list.html"
    partial_template_name = "inventories/stocks/partials/table.html"
    # filterset_class = TransactionFilter

    # Search configuration
    search_fields = ["batch_no"]

    # Export configuration
    export_name = "stock_transactions"
    export_permission = []  # Empty list means no specific permissions required

    # HTMX configuration, override default to prevent htmx target conflict
    # Map this with own partial element id, use this when under single page there is more than 1 htmx table
    htmx_target = 'table-content-partial'

    def get_queryset(self) -> QuerySet:
        queryset = super().get_queryset()
        # Apply additional filter for this specific view
        return queryset.filter(stock__pk=self.kwargs.get('pk'))

    def get_htmx_base_url(self):
        """
        Override to provide the dynamic URL based on the current item's PK.
        This URL will be used for all HTMX requests and querystring operations.
        """
        pk = self.kwargs.get('pk')
        if pk:
            return reverse("inventories:stocks:transaction_list", kwargs={"pk": pk})
        return None


class StockDetailHomeView(CoreDetailView):
    model = Stock
    template_name = 'inventories/stocks/mains/home.html'


class StockDetailView(CoreDetailView):
    model = Stock
    template_name = 'inventories/stocks/partials/detail.html'


class StockDataTableDetailView(CoreDataTableDetailView):
    model = Stock
    table_class = StockDetailTable
    context_object_name = "stock"
    partial_view = StockDetailHomeView
    search_fields = ["batch_no"]
    filterset_class = StockDataTableFilter

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get the warehouse from the stock object
        warehouse = self.object.warehouse

        # Add custom breadcrumb data for the hierarchical path
        context['custom_breadcrumb'] = {
            'path': [
                {
                    'name': 'Inventories',
                    'url': None  # Module with submenus should not be clickable
                },
                {
                    'name': 'Warehouses',
                    'url': reverse('inventories:warehouses:list')
                },
                {
                    'name': 'Detail',
                    'url': None
                },
                {
                    'name': warehouse.name,
                    'url': reverse('inventories:warehouses:panel', kwargs={'pk': warehouse.pk})
                },
                {
                    'name': 'Stock',
                    'url': None
                },
                {
                    'name': 'Detail',
                    'url': None
                },
                {
                    'name': self.get_object_identifier(),
                    'url': self.request.path_info
                }
            ]
        }

        return context

    def get_queryset(self):

        stock = self.model.objects.get(pk=self.kwargs["pk"])

        return self.model.objects.filter(
            warehouse=stock.warehouse,
            item=stock.item,
        )

    def get_filterset(self, filterset_class):
        """
        To default the filter value especially on stock_on_hand filter field
        """
        data = self.request.GET.copy()

        # Only set initial if not already filtered
        if "stock_on_hand" not in data:
            data["stock_on_hand"] = "gt_zero"

        return filterset_class(data=data, queryset=self.get_queryset(), request=self.request)


# class StockDetailView(LoginRequiredMixin, PermissionRequiredMixin, SingleTableMixin, CoreDetailView):
#     """Page to display selected Stock based on given Stock's pk.
#     This page use DataTables server side."""

#     model = Stock
#     template_name = "inventories/stocks/detail.html"

#     table_class = StockSideTable

#     header_title = "Stocks"
#     selected_page = "inventories"
#     selected_subpage = "warehouses"

#     permission_required = (
#         "settings.view_warehouse",
#         "inventories.view_item",
#         "inventories.view_stock",
#     )

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context["header_title"] = self.header_title + f" - {self.object.warehouse.name} :: {self.object.item.code}"
#         return context


# stock_detail_view = StockDetailView.as_view()


# ##################
# # FOR DataTables #
# ##################


# class StockDetailDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in detail page."""

#     model = Stock
#     table_class = StockSideTable
#     filterset_class = StockFilter

#     permission_required = (
#         "settings.view_warehouse",
#         "inventories.view_item",
#         "inventories.view_stock",
#     )

#     def get_queryset(self):

#         stock = self.model.objects.get(pk=self.kwargs["pk"])

#         return self.model.objects.filter(
#             warehouse=stock.warehouse,
#             item=stock.item,
#         )


# stock_detail_datatables_view = StockDetailDataTablesView.as_view()


# class StockTransactionDataTablesView(LoginRequiredMixin, PermissionRequiredMixin, CoreDataTablesView):
#     """JSON for DataTables in list page."""

#     model = Transaction
#     table_class = StockTransactionDataTables
#     filterset_class = StockTransactionFilter

#     permission_required = (
#         "inventories.view_stock",
#         "inventories.view_transaction",
#     )

#     def get_queryset(self):
#         return self.model.objects.filter(stock__pk=self.kwargs["pk"])


# stock_transaction_datatables_view = StockTransactionDataTablesView.as_view()


# ############
# # FOR HTMX #
# ############


# class StockTransactionsListView(
#     LoginRequiredMixin, PermissionRequiredMixin, FilterView, SingleTableMixin, CoreListView
# ):
#     """Partial page to show all Stock's Transaction based on given Stock's pk."""

#     model = Transaction
#     template_name = "inventories/stocks/partials/htmx/_transactions.html"
#     table_class = StockTransactionDataTables
#     filterset_class = StockTransactionFilter

#     # To prevent query all object as it will use ajax call in template
#     object_list = Transaction.objects.none()

#     permission_required = (
#         "inventories.view_stock",
#         "inventories.view_transaction",
#     )

#     def get_queryset(self):
#         return self.model.objects.select_related("created_by", "stock", "uom").filter(stock__pk=self.kwargs["pk"])

#     def get_context_data(self, **kwargs):
#         context = super().get_context_data(**kwargs)
#         context["stock"] = Stock.objects.get(pk=self.kwargs["pk"])
#         return context


# stock_transactions_list_view = StockTransactionsListView.as_view()
