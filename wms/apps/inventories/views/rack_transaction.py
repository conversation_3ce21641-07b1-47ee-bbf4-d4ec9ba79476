import json

from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import get_object_or_404, render, redirect
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_POST

from wms.apps.rackings.models import RackTransaction


def rack_transaction_unfreeze_form(request, pk):
    """
    Display the confirmation form for unfreezing (deleting) a rack transaction.
    """
    rack_transaction = get_object_or_404(RackTransaction, pk=pk)

    # Check if the transaction is freezed
    if not rack_transaction.is_freezed:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Only freezed transactions can be unfrozen.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request, _("Only freezed transactions can be unfrozen."))
        return HttpResponse(status=400, content="Only freezed transactions can be unfrozen.")

    context = {
        'rack_transaction': rack_transaction,
        'request': request,
    }

    return render(request, 'inventories/rackstorages/partials/rack_transaction_unfreeze_form.html', context)


@require_POST
def rack_transaction_unfreeze(request, pk):
    """
    Unfreeze (delete) a rack transaction.
    """
    rack_transaction = get_object_or_404(RackTransaction, pk=pk)

    # Check if the transaction is freezed
    if not rack_transaction.is_freezed:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Only freezed transactions can be unfrozen.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request, _("Only freezed transactions can be unfrozen."))
        return HttpResponse(status=400, content="Only freezed transactions can be unfrozen.")

    try:
        # Store the rackstorage ID before deleting the transaction
        rackstorage_id = rack_transaction.rackstorage.id

        # Delete the transaction
        rack_transaction.delete()

        # For HTMX requests, close the modal and redirect
        if request.headers.get('HX-Request'):
            # Create the redirect URL to the rackstorage detail page
            redirect_url = reverse('inventories:rackstorages:panel', kwargs={'pk': rackstorage_id})

            trigger_data = {
                "closeModalEvent": None,
                "showNotificationEvent": {
                    "message": "Transaction unfrozen successfully!",
                    "type": "success"
                }
            }

            headers = {
                'HX-Trigger': json.dumps(trigger_data),
                'HX-Redirect': redirect_url
            }

            return HttpResponse(status=200, headers=headers)

        # For regular form submissions, redirect to the rackstorage detail page
        messages.success(request, _("Transaction unfrozen successfully!"))
        return redirect(reverse('inventories:rackstorages:panel', kwargs={'pk': rackstorage_id}))
    except Exception as e:
        messages.error(request, _("Failed to unfreeze transaction: %(error)s") % {'error': str(e)})
        return HttpResponse(status=500, content=f"Error unfreezing transaction: {str(e)}")
