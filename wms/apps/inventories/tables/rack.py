# from datetime import datetime
# from decimal import Decimal
# from typing import Any

# from django.urls import reverse
# from django.utils.formats import localize
# from django.utils.html import format_html
# from django.utils.timezone import localtime
from django.utils.translation import gettext_lazy as _

import django_tables2 as tables
from django.urls import reverse, NoReverseMatch
from django.utils.html import format_html

from wms.cores.models import DISPLAY_EMPTY_VALUE
# from wms.cores.tables import TABLE_ATTRS_CLASS
from wms.cores.utils import localtime_now, normalize_decimal

from wms.cores.columns import HTMXColumn

from wms.apps.rackings.models import Rack, RackStorage, RackTransaction

# from ..models import Stock


#####################################################################
# Inner Datatable
#####################################################################

class WarehouseRacksTable(tables.Table):
    selectable = False

    # code = tables.Column(verbose_name=_("Code"), accessor="code")
    # code = tables.LinkColumn(
    #     "inventories:warehouses:panel",
    #     args=[tables.utils.A("pk")],  # Pass primary key
    #     accessor="code"  # Refers to the @property method
    # )
    full_name = tables.Column(
        verbose_name=_("Full Name"),
        accessor="full_name",
        orderable=False,
        linkify=("inventories:racks:panel", {"pk": tables.A("pk")})
    )

    class Meta:
        model = Rack
        order_by = 'path'
        template_name = "tables/table_htmx.html"
        fields = [
            "rack_type",
            "full_name",
        ]
        sequence = [
            "rack_type",
            "full_name",
        ]

    def __init__(self, *args, warehouse=None, **kwargs):
        self.warehouse = warehouse
        super().__init__(*args, **kwargs)

    def render_item_code(self, value: Rack) -> str:
        return f"{value.full_name}"


class RackDetailTable(tables.Table):
    full_name = HTMXColumn(
        url_name="inventories:racks:detail_home",
        target_id="detail-panel",
        verbose_name=_("Full Name"),
        push_url=True,
        push_url_name="inventories:racks:panel",
    )
    # expiry_date = tables.Column(
    #     verbose_name=_("Expiry Date"),
    #     accessor="expiry_date",
    #     orderable=True,
    # )
    # stock_on_hand = tables.Column(
    #     verbose_name=_("SOH"),
    #     accessor="db_balance",
    #     orderable=True,
    # )

    class Meta:
        model = Rack
        fields = (
            "full_name",
            # "stock_on_hand"
        )
        order_by = '-created_at'
        template_name = "tables/table_htmx.html"

    def __init__(self, *args, object_pk=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.object_pk = object_pk

    # def render_stock_on_hand(self, value):
    #     """
    #     Formats the 'stock_on_hand' value to display with a thousand separators.
    #     """
    #     return intcomma(value)

    # def order_stock_on_hand(self, queryset, is_descending):
    #     """
    #     Custom ordering for stock_on_hand column.
    #     This uses a subquery to annotate the queryset with the sum of transactions.
    #     """
    #     from wms.apps.inventories.models import Transaction

    #     # Create a subquery to get the sum of system_quantity for each item
    #     stock_subquery = Transaction.objects.filter(
    #         stock=OuterRef('pk')
    #     ).values('stock').annotate(
    #         total=Sum('system_quantity')
    #     ).values('total')

    #     # Annotate the queryset with the result of the subquery
    #     # Use Coalesce to handle NULL values (items with no stock)
    #     queryset = queryset.annotate(
    #         total_balance=Coalesce(
    #             Subquery(stock_subquery),
    #             Value(0),
    #             output_field=DecimalField()
    #         )
    #     )

    #     # Order by the annotated field
    #     return queryset.order_by('-total_balance' if is_descending else 'total_balance'), True


class RackStorageTable(tables.Table):
    selectable = False

    full_name = tables.Column(
        verbose_name=_("Location"),
        accessor="rack.full_name",
        linkify=("inventories:rackstorages:panel", {"pk": tables.A("pk")})
    )
    item_code = tables.Column(
        verbose_name=_("Product Code"),
        accessor="stock.item.code",
    )
    item_name = tables.Column(
        verbose_name=_("Description"),
        accessor="stock.item.name",
    )
    batch_no = tables.Column(
        verbose_name=_("Batch"),
        accessor="stock.batch_no",
    )
    expiry_date = tables.Column(
        verbose_name=_("Expiry Date"),
        accessor="stock.expiry_date",
    )
    physical_rack_transaction_balance = tables.Column(
        verbose_name=_("Physical QTY"),
        accessor="get_current_physical_rack_transaction_balance",
        # orderable=True,
    )
    available_rack_transaction_balance = tables.Column(
        verbose_name=_("Available QTY"),
        accessor="get_current_available_rack_transaction_balance",
        # orderable=True,
    )
    reserved_rack_transaction_balance = tables.Column(
        verbose_name=_("Reserved QTY"),
        accessor="get_current_reserved_rack_transaction_balance",
        # orderable=True,
    )
    freezed_rack_transaction_balance = tables.Column(
        verbose_name=_("Freezed QTY"),
        accessor="get_current_freezed_rack_transaction_balance",
        # orderable=True,
    )

    class Meta:
        model = RackStorage
        order_by = "rack__full_name"  # Must follow the first row display in the table
        order_by_field = (
            "rack__full_name"  # To ensure the JSON returns consistent results based on order_by line above
        )
        template_name = "tables/table_htmx.html"
        fields = ()
        sequence = [
            "full_name",
            "item_code",
            "item_name",
            "batch_no",
            "expiry_date",
            "physical_rack_transaction_balance",
            "available_rack_transaction_balance",
            "reserved_rack_transaction_balance",
            "freezed_rack_transaction_balance",
        ]

    def render_full_name(self, value, record):
        return record.rack.full_name

    def render_item_code(self, value, record):
        return record.stock.item.code

    def render_item_name(self, value, record):
        return record.stock.item.name

    def render_batch_no(self, value, record):
        return record.stock.batch_no

    def render_expiry_date(self, value, record):
        return record.stock.expiry_date

    def get_current_physical_rack_transaction_balance(self, value, record):
        return record.get_current_physical_rack_transaction_balance

    def render_available_rack_transaction_balance(self, value, record):
        return record.get_current_available_rack_transaction_balance
        # return RackTransaction.objects.balance_by_stock(rack=record.rack, balance_type="available")

    def render_reserved_rack_transaction_balance(self, value, record):
        return record.get_current_reserved_rack_transaction_balance
        # return RackTransaction.objects.balance_by_stock(rack=record.rack, balance_type="reserved")

    def render_freezed_rack_transaction_balance(self, value, record):
        return record.get_current_freezed_rack_transaction_balance


###########################################################################
# RackStorage + RackTransaction
###########################################################################


class RackStorageDetailTable(tables.Table):
    selectable = False

    full_name = tables.LinkColumn(
        "inventories:rackstorages:panel",
        args=[tables.utils.A("pk")],
        verbose_name=_("Location"),
        accessor="rack.full_name"
    )
    item_code = tables.Column(
        verbose_name=_("Product Code"),
        accessor="stock.item.code",
    )
    batch_no = tables.Column(
        verbose_name=_("Batch"),
        accessor="stock.batch_no",
    )

    class Meta:
        model = RackStorage
        fields = (
            "full_name",
            "item_code",
            "batch_no",
        )
        order_by = '-created_at'
        template_name = "tables/table_htmx.html"

    def __init__(self, *args, object_pk=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.object_pk = object_pk

    def render_full_name(self, value, record):
        return record.rack.full_name

    def render_item_code(self, value, record):
        return record.stock.item.code

    def render_batch_no(self, value, record):
        return record.stock.batch_no


class RackTransactionTable(tables.Table):

    selectable = False

    item_code = tables.Column(
        verbose_name=_("ITEM CODE"),
        accessor="rackstorage.stock.item.code",
    )
    quantity = tables.Column(
        verbose_name=_("QTY"),
        accessor="quantity",
    )
    rack_transaction_balance = tables.Column(
        verbose_name=_("Balance"),
        accessor="quantity",
        # orderable=True,
    )

    transaction_date = tables.TemplateColumn(
        verbose_name=_("TRANSACTION DATE"),
        accessor="transaction_datetime",
        template_name="inventories/rackstorages/partials/datatable_column/_column_transaction_date.html",
    )

    actions = tables.TemplateColumn(
        verbose_name=_("Actions"),
        template_name="inventories/rackstorages/partials/datatable_column/_column_actions.html",
        orderable=False,
        default=DISPLAY_EMPTY_VALUE,
    )

    class Meta:
        model = RackTransaction
        order_by = "rackstorage__rack__full_name"  # Must follow the first row display in the table
        order_by_field = (
            "rackstorage__rack__full_name"  # To ensure the JSON returns consistent results based on order_by line above
        )
        template_name = "tables/table_htmx.html"
        row_attrs = {
            "class": lambda record: "bg-yellow-100" if record.is_freezed else ""
        }
        fields = (
            "rackstorage",
            "type",
            "is_reserved",
            "is_freezed",
            "goods_received_note_item",
            "warehouse_release_order_item",
        )
        sequence = [
            "transaction_date",
            "item_code",
            "rackstorage",
            "quantity",
            "rack_transaction_balance",
            "type",
            "is_reserved",
            "is_freezed",
            "goods_received_note_item",
            "warehouse_release_order_item",
            "actions",
        ]

    def value_item_code(self, value, record):
        return record.rackstorage.stock.item.code

    def render_quantity(self, value, record):
        return normalize_decimal(record.quantity)

    def render_rack_transaction_balance(self, value, record):
        return record.get_current_rack_transaction_balance


# class WarehouseRacksStockTable(tables.Table):
#     """Table used on Warehouse's Rack's Stock list page."""

#     warehouse = tables.Column(verbose_name=_("Warehouse"), accessor="warehouse__name", orderable=False)
#     rack_balance = tables.Column(verbose_name=_("Balance"), accessor="rack_balance")
#     actions = tables.TemplateColumn(
#         verbose_name=_("Actions"),
#         orderable=False,
#         template_name="inventories/racks/partials/tables/warehouseracksstocktable/_actions.html",
#         attrs={"th": {"class": "text-right"}, "td": {"class": "text-right"}},
#         exclude_from_export=True,
#     )

#     class Meta:
#         model = Stock
#         order_by = "item"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {
#             "class": TABLE_ATTRS_CLASS,
#             "id": "warehouse_rack_stock_datatables",
#             "style": "display: none;",
#             "thead": {"class": ""},
#         }
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "item",
#             "warehouse",
#             "batch_no",
#             "expiry_date",
#         ]
#         sequence = [
#             "item",
#             "warehouse",
#             "batch_no",
#             "expiry_date",
#             "rack_balance",
#             "actions",
#         ]

#     def __init__(self, *args, **kwargs):
#         self.rack = kwargs.pop("rack")
#         super().__init__(*args, **kwargs)

#     def render_rack_balance(self, value, record):
#         return f"{normalize_decimal(value)}"

#     def render_actions(self, column, record: Stock, table, value, bound_column, bound_row) -> Any:
#         """Add permission checking into actions column."""

#         column.extra_context = {
#             "rack": self.rack,
#             "stock": record,
#             "year": localtime_now().year,
#             "month": localtime_now().month,
#         }
#         return column.render(record, table, value, bound_column, **{"bound_row": bound_row})


# class RackTransactionInOutTable(tables.Table):
#     """Table used on Warehouse's Rack's Stock's Transaction partial page."""

#     transaction_date = tables.Column(
#         verbose_name=_("DATE"),
#         accessor="transaction_datetime",
#         order_by=("transaction_datetime", "created"),
#         attrs={
#             "th": {
#                 "class": "table-tooltip",
#                 "data-toggle": "tooltip",
#                 "data-placement": "top",
#                 "data-original-title": _("Transaction Date Time"),
#             }
#         },
#     )
#     batch_no = tables.Column(
#         verbose_name=_("BN"),
#         accessor="rackstorage__stock",
#         attrs={
#             "th": {
#                 "class": "table-tooltip",
#                 "data-toggle": "tooltip",
#                 "data-placement": "top",
#                 "data-original-title": _("Batch Number"),
#             }
#         },
#     )
#     expiry_date = tables.Column(
#         verbose_name=_("EXP"),
#         accessor="rackstorage__stock",
#         attrs={
#             "th": {
#                 "class": "table-tooltip",
#                 "data-toggle": "tooltip",
#                 "data-placement": "top",
#                 "data-original-title": _("Expiry Date"),
#             }
#         },
#     )
#     stock_in = tables.Column(
#         verbose_name=_("IN"),
#         accessor="quantity",
#         attrs={
#             "th": {
#                 "class": "table-tooltip",
#                 "data-toggle": "tooltip",
#                 "data-placement": "top",
#                 "data-original-title": _("Stock In"),
#             }
#         },
#     )
#     stock_out = tables.Column(
#         verbose_name=_("OUT"),
#         accessor="quantity",
#         attrs={
#             "th": {
#                 "class": "table-tooltip",
#                 "data-toggle": "tooltip",
#                 "data-placement": "top",
#                 "data-original-title": _("Stock Out"),
#             }
#         },
#     )
#     location = tables.Column(verbose_name=_("LOCATION"), empty_values=())
#     transaction_type = tables.Column(
#         verbose_name=_("TYPE"),
#         empty_values=(),
#         attrs={
#             "th": {
#                 "class": "table-tooltip",
#                 "data-toggle": "tooltip",
#                 "data-placement": "top",
#                 "data-original-title": _("Transaction Type"),
#             }
#         },
#     )
#     reserved = tables.Column(
#         verbose_name=_("RSVD"),
#         accessor="quantity",
#         attrs={
#             "th": {
#                 "class": "table-tooltip",
#                 "data-toggle": "tooltip",
#                 "data-placement": "top",
#                 "data-original-title": _("Reserved"),
#             }
#         },
#     )
#     physical_balance = tables.Column(
#         verbose_name=_("PB"),
#         accessor="quantity",
#         attrs={
#             "th": {
#                 "class": "table-tooltip",
#                 "data-toggle": "tooltip",
#                 "data-placement": "top",
#                 "data-original-title": _("Physical Balance"),
#             }
#         },
#     )
#     available_balance = tables.Column(
#         verbose_name=_("AB"),
#         accessor="quantity",
#         attrs={
#             "th": {
#                 "class": "table-tooltip",
#                 "data-toggle": "tooltip",
#                 "data-placement": "top",
#                 "data-original-title": _("Available Balance"),
#             }
#         },
#     )
#     remark = tables.Column(verbose_name=_("REMARK"), empty_values=())
#     created_by = tables.Column(verbose_name=_("CREATED BY"))

#     class Meta:
#         model = RackTransaction
#         order_by = "transaction_date"  # Must follow the first row display in the table
#         order_by_field = (
#             "transaction_date"  # To ensure the JSON returns consistent results based on order_by line above
#         )
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {
#             "class": TABLE_ATTRS_CLASS + " table-bordered table-sm",
#             "id": "rack_transaction_in_out_datatables",
#             "style": "display: none;",
#             "thead": {"class": "bg-info"},
#         }
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "transaction_date",
#             "remark",
#             "created_by",
#         ]
#         sequence = [
#             "transaction_date",
#             "location",
#             "transaction_type",
#             "batch_no",
#             "expiry_date",
#             "stock_in",
#             "stock_out",
#             "reserved",
#             "physical_balance",
#             "available_balance",
#             "remark",
#             "created_by",
#         ]

#     def __init__(self, *args, **kwargs) -> None:
#         self.rack = kwargs.pop("rack")
#         self.year = kwargs.pop("year")
#         self.month = kwargs.pop("month")
#         super().__init__(*args, **kwargs)

#     def render_transaction_date(self, value: datetime, record: RackTransaction):
#         formatted_datetime = localtime(value)

#         return format_html(
#             """
#             <span class="table-tooltip" data-toggle="tooltip"
#             data-placement="top" data-original-title="{}"
#             >{}</span><div class="d-none"> {}</div>
#             """,
#             localize(formatted_datetime),
#             localize(formatted_datetime.date()),
#             localize(formatted_datetime.time()),
#         )

#     def render_location(self, value, record: RackTransaction) -> str:
#         if self.rack != record.rackstorage.rack:
#             return f"{self.rack} 【 {record.rackstorage.rack} 】"
#         else:
#             return f"{record.rackstorage.rack}"

#     def render_transaction_type(self, value, record: RackTransaction) -> str:
#         return record.html_type_display

#     def render_stock_in(self, value: Decimal, record: RackTransaction) -> str:
#         return normalize_decimal(record.system_stock_in) or DISPLAY_EMPTY_VALUE

#     def render_stock_out(self, value: Decimal, record: RackTransaction) -> str:
#         if record.type == RackTransaction.Type.RESERVED:
#             return DISPLAY_EMPTY_VALUE

#         return normalize_decimal(record.system_stock_out) or DISPLAY_EMPTY_VALUE

#     def render_reserved(self, value: Decimal, record: RackTransaction) -> str:
#         if record.type != RackTransaction.Type.RESERVED:
#             return DISPLAY_EMPTY_VALUE

#         return normalize_decimal(record.system_stock_out) or DISPLAY_EMPTY_VALUE

#     def render_physical_balance(self, value: Decimal, record: RackTransaction) -> str:
#         balance = record.get_physical_balance_by_rack(self.rack)
#         return balance

#     def render_available_balance(self, value: Decimal, record: RackTransaction) -> str:
#         balance = record.get_balance_by_rack(self.rack)
#         return balance

#     def render_batch_no(self, value: Stock, record: RackTransaction) -> str:
#         return value.batch_no

#     def render_expiry_date(self, value: Stock, record: RackTransaction) -> str:
#         return value.expiry_date or DISPLAY_EMPTY_VALUE

#     def render_created_by(self, value, record: RackTransaction):
#         return value

#     def render_remark(self, value: str, record: RackTransaction):
#         if record.goods_received_note is not None:
#             numbering = record.goods_received_note.numbering
#             grn_racking_url = reverse(
#                 "receives:racking_goods_received_notes:detail", kwargs={"pk": record.goods_received_note.pk}
#             )
#             return f"<a href={grn_racking_url}>{numbering}</a>"

#         if record.warehouse_release_order is not None:
#             numbering = record.warehouse_release_order.numbering
#             release_order_url = record.warehouse_release_order.get_absolute_url()
#             return f"<a href={release_order_url}>{numbering}</a>"

#         return value
