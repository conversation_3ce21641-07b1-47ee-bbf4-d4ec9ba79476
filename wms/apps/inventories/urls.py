from django.urls import include, path

# from wss.apps.inventories.views import (
#     export_all_item_stocks_to_xlsx_view,
#     item_available_uom_dropdown_list_view,
#     item_barcode_detail_view,
#     item_barcode_reset_view,
#     item_barcode_update_view,
#     item_create_view,
#     item_datatables_view,
#     item_delete_view,
#     item_detail_datatables_view,
#     item_detail_view,
#     item_dropdown_list_view,
#     item_history_list_datatables_view,
#     item_history_list_view,
#     item_history_modified_view,
#     item_info_detail_view,
#     item_info_update_view,
#     item_list_view,
#     item_rack_list_view,
#     item_rack_storage_datatables_view,
#     item_scan_barcode_view,
#     item_setting_detail_view,
#     item_setting_update_view,
#     item_stock_list_datatables_view,
#     item_stock_list_view,
#     item_update_view,
#     item_warehouse_stock_rack_list_datatables_view,
#     item_warehouse_stock_rack_list_view,
#     rack_stock_in_out_create_view,
#     rack_transaction_detail_view,
#     rack_transaction_in_out_datatables_view,
#     rack_transaction_in_out_list_view,
#     rack_transaction_monthly_in_out_datatables_view,
#     stock_detail_datatables_view,
#     stock_detail_view,
#     stock_transaction_datatables_view,
#     stock_transactions_list_view,
#     warehouse_detail_datatables_view,
#     warehouse_detail_view,
#     warehouse_items_datatables_view,
#     warehouse_items_list_view,
#     warehouse_list_view,
#     warehouse_racks_datatables_view,
#     warehouse_racks_detail_datatables_view,
#     warehouse_racks_detail_view,
#     warehouse_racks_list_view,
#     warehouse_racks_stock_datatables_view,
#     warehouse_racks_stock_list_view,
#     warehouse_stock_detail_datatables_view,
#     warehouse_stock_detail_view,
# )

from wms.apps.inventories.views import (
    #Item
    ItemListView,
    # ItemDeleteView,
    # ItemCreateView,
    ItemDataTableDetailView,
    # ItemUpdateView,
    item_delete_form,
    item_delete,

    # Stock
    StockTransactionListView,
    StockDetailHomeView,
    StockDetailView,
    StockDataTableDetailView,

    # Warehouse
    WarehouseListView,
    WarehouseDetailHomeView,
    WarehouseDetailView,
    WarehouseDataTableDetailView,
    WarehouseItemListView,
    WarehouseRackView,
    WarehouseRackListView,

    # Rack
    RackStorageListView,
    RackDetailHomeView,
    RackDetailView,
    RackDataTableDetailView,
    OuterRackTransactionAdjustmentView,

    # RackStorage
    RackStorageDataTableDetailView,
    RackStorageDetailHomeView,
    RackStorageDetailView,
    RackTransactionListView,
    RackTransactionAdjustmentView,
    RackTransactionTransferView,
)
from wms.apps.inventories.views.rack_transaction import rack_transaction_unfreeze_form, rack_transaction_unfreeze
from wms.apps.inventories.views.item import ItemDetailView, ItemDetailHomeView, ItemEventView, ItemCreateView, ItemUpdateView, ItemStockView, ItemStockListView

app_name = "inventories"

items_urlpatterns = [
    path("", ItemListView.as_view(), name="list"),
    path("create/", ItemCreateView.as_view(), name="create"),
    path("<int:pk>/update/", ItemUpdateView.as_view(), name="update"),
    path("<int:pk>/", ItemDataTableDetailView.as_view(), name="panel"),
    path("<int:pk>/home/", ItemDetailHomeView.as_view(), name="detail_home"),
    path("<int:pk>/detail/", ItemDetailView.as_view(), name="detail"),
    path("<int:pk>/stock/", ItemStockView.as_view(), name="stock"),
    path("<int:pk>/stock/list/", ItemStockListView.as_view(), name="stock_list"),
    path("<int:pk>/event/", ItemEventView.as_view(), name="event"),
    path("<int:pk>/delete-form/", view=item_delete_form, name="delete_form"),
    path("<int:pk>/delete/", view=item_delete, name="delete"),
    # path("", view=item_list_view, name="list"),
    # path("create/", view=item_create_view, name="create"),
    # path("update/<str:slug>/", view=item_update_view, name="update"),
    # path("delete/<str:slug>/", view=item_delete_view, name="delete"),
    # path("detail/<str:slug>/", view=item_detail_view, name="detail"),
    # path("detail/<str:slug>/info/", view=item_info_detail_view, name="info"),
    # path("detail/<str:slug>/info/update/", view=item_info_update_view, name="info_update"),
    # path("detail/<str:slug>/setting/", view=item_setting_detail_view, name="setting"),
    # path("detail/<str:slug>/barcode/update/", view=item_barcode_update_view, name="barcode_update"),
    # path("detail/<str:slug>/barcode/reset/", view=item_barcode_reset_view, name="barcode_reset"),
    # path("detail/<str:slug>/barcode/", view=item_barcode_detail_view, name="barcode"),
    # path(
    #     "detail/<str:slug>/setting/update/",
    #     view=item_setting_update_view,
    #     name="setting_update",
    # ),
    # path("detail/<str:slug>/stocks/", view=item_stock_list_view, name="stocks"),
    # path(
    #     "detail/<str:slug>/item-stock-list-datatables-detail/",
    #     view=item_stock_list_datatables_view,
    #     name="item_stock_list_datatables_detail",
    # ),
    # path(
    #     "detail/<str:slug>/export-all-item-stocks/",
    #     view=export_all_item_stocks_to_xlsx_view,
    #     name="export_all_item_stocks",
    # ),
    # path("detail/<int:pk>/history/", view=item_history_list_view, name="history"),
    # path("detail/<int:pk>/history/popup-modified", view=item_history_modified_view, name="history-modified"),
    # path("detail/<int:pk>/datatables-history/", view=item_history_list_datatables_view, name="datatables-history"),
    # path("datatables/", view=item_datatables_view, name="datatables"),
    # path("datatables-detail/", view=item_detail_datatables_view, name="datatables-detail"),
    # path("dropdown/uoms/", view=item_available_uom_dropdown_list_view, name="uoms_dropdown"),
    # path("dropdown/items/", view=item_dropdown_list_view, name="items_dropdown"),
    # path("<str:barcode>/barcode/", view=item_scan_barcode_view, name="scan_barcode"),
    # path("detail/<str:slug>/rack/", view=item_rack_list_view, name="racks"),
    # path("datatables/<str:slug>/rack/", view=item_rack_storage_datatables_view, name="datatables-racks"),
    # path(
    #     "warehouse/<str:warehouse_slug>/stock/<int:stock_pk>/racks/",
    #     view=item_warehouse_stock_rack_list_view,
    #     name="warehouse_stock_racks",
    # ),
    # path(
    #     "warehouse/<str:warehouse_slug>/stock/<int:stock_pk>/datatables-racks/",
    #     view=item_warehouse_stock_rack_list_datatables_view,
    #     name="datatables-stock-racks",
    # ),
]

stock_urlpatterns = [
    path("<int:pk>/", StockDataTableDetailView.as_view(), name="panel"),
    path("<int:pk>/home/", StockDetailHomeView.as_view(), name="detail_home"),
    path("<int:pk>/detail/", StockDetailView.as_view(), name="detail"),
    path("<int:pk>/transaction/list/", StockTransactionListView.as_view(), name="transaction_list"),
#     path("detail/<int:pk>/", view=stock_detail_view, name="detail"),
#     path("detail/<int:pk>/transactions/", view=stock_transactions_list_view, name="transactions"),
#     path("detail/<int:pk>/datatables-detail/", view=stock_detail_datatables_view, name="datatables-detail"),
#     path(
#         "detail/<int:pk>/transaction-datatables-detail/",
#         view=stock_transaction_datatables_view,
#         name="stock_transaction_datatables_detail",
#     ),
]


warehouses_urlpatterns = [
    path("", WarehouseListView.as_view(), name="list"),
    path("<int:pk>/", WarehouseDataTableDetailView.as_view(), name="panel"),
    path("<int:pk>/home/", WarehouseDetailHomeView.as_view(), name="detail_home"),
    path("<int:pk>/detail/", WarehouseDetailView.as_view(), name="detail"),
    path("<int:pk>/item/list/", WarehouseItemListView.as_view(), name="item_list"),
    path("<int:pk>/rack/", WarehouseRackView.as_view(), name="rack"),
    path("<int:pk>/rack/list/", WarehouseRackListView.as_view(), name="rack_list"),
#     path("", view=warehouse_list_view, name="list"),
#     path("detail/<str:slug>/", view=warehouse_detail_view, name="detail"),
#     path("detail/<str:slug>/items/", view=warehouse_items_list_view, name="items"),
#     path("datatables/<str:slug>/items/", view=warehouse_items_datatables_view, name="datatables-items"),
#     path("detail/<str:slug>/racks/", view=warehouse_racks_list_view, name="racks"),
#     path("datatables/<str:slug>/racks/", view=warehouse_racks_datatables_view, name="datatables-racks"),
#     path("detail/<str:warehouse_slug>/racks/<int:rack_pk>/", view=warehouse_racks_detail_view, name="racks_detail"),
#     path(
#         "detail/<str:warehouse_slug>/racks/<int:rack_pk>/rack-datatables-detail/",
#         view=warehouse_racks_detail_datatables_view,
#         name="rack_datatables_detail",
#     ),
#     path("detail/<str:warehouse_slug>/item/<str:item_slug>/", view=warehouse_stock_detail_view, name="stock_detail"),
#     path("datatables-detail/", view=warehouse_detail_datatables_view, name="datatables-detail"),
#     path(
#         "detail/<str:warehouse_slug>/item/<str:item_slug>/stock-datatables-detail/",
#         view=warehouse_stock_detail_datatables_view,
#         name="stock_datatables_detail",
#     ),
]


rack_urlpatterns = [
    # Rack + RackStorage
    path("<int:pk>/", RackDataTableDetailView.as_view(), name="panel"),
    path("<int:pk>/home/", RackDetailHomeView.as_view(), name="detail_home"),
    path("<int:pk>/detail/", RackDetailView.as_view(), name="detail"),
    path("<int:pk>/rackstorage/list/", RackStorageListView.as_view(), name="rackstorage_list"),
    path(
        "<int:pk>/rack-transaction/adjustment",
        OuterRackTransactionAdjustmentView.as_view(),
        name="outer_rack_transaction_adjustment"
    ),
#     path("detail/<str:warehouse_slug>/racks/<int:rack_pk>/stocks", view=warehouse_racks_stock_list_view, name="stocks"),
#     path(
#         "detail/<str:warehouse_slug>/racks/<int:rack_pk>/stock-datatables-detail/",
#         view=warehouse_racks_stock_datatables_view,
#         name="warehouse_racks_stock_datatables_detail",
#     ),
#     path("<int:rack_pk>/stock/<int:pk>/", view=rack_transaction_detail_view, name="transaction_detail"),
#     path(
#         "<int:rack_pk>/stock/<int:pk>/year/<int:year>/month/<int:month>/",
#         view=rack_transaction_detail_view,
#         name="transaction_detail",
#     ),
#     path(
#         "<int:rack_pk>/stock/<int:pk>/in-out/",
#         view=rack_transaction_in_out_list_view,
#         name="in_out",
#     ),
#     path(
#         "<int:rack_pk>/stock/<int:pk>/in-out/year/<int:year>/month/<int:month>/",
#         view=rack_transaction_in_out_list_view,
#         name="in_out",
#     ),
#     # FOR DataTables
#     path(
#         "<int:rack_pk>/stock/<int:pk>/in-out-datatables/",
#         view=rack_transaction_in_out_datatables_view,
#         name="in_out_datatables",
#     ),
#     path(
#         "<int:rack_pk>/stock/<int:pk>/in-out-datatables/year/<int:year>/month/<int:month>/",
#         view=rack_transaction_monthly_in_out_datatables_view,
#         name="in_out_datatables",
#     ),
#     path("<int:pk>/stock-in-out/", view=rack_stock_in_out_create_view, name="stock_in_out"),
]

rackstorage_urlpatterns = [
    # RackStorage + RackTransaction
    path("<int:pk>/", RackStorageDataTableDetailView.as_view(), name="panel"),
    path("<int:pk>/home/", RackStorageDetailHomeView.as_view(), name="detail_home"),
    path("<int:pk>/detail/", RackStorageDetailView.as_view(), name="detail"),
    path(
        "<int:pk>/rack-transaction/list",
        RackTransactionListView.as_view(),
        name="rack_transaction_list"
    ),
    path(
        "<int:pk>/rack-transaction/adjustment",
        RackTransactionAdjustmentView.as_view(),
        name="rack_transaction_adjustment"
    ),
    path(
        "<int:pk>/rack-transaction/transfer",
        RackTransactionTransferView.as_view(),
        name="rack_transaction_transfer"
    ),
]

urlpatterns = [
    path("items/", include((items_urlpatterns, "inventories.item"), namespace="items")),
    path("warehouses/", include((warehouses_urlpatterns, "inventories.warehouse"), namespace="warehouses")),
    path("stocks/", include((stock_urlpatterns, "inventories.stock"), namespace="stocks")),
    path("racks/", include((rack_urlpatterns, "inventories.rack"), namespace="racks")),
    path("rackstorages/", include((rackstorage_urlpatterns, "inventories.rackstorage"), namespace="rackstorages")),
    # Rack Transaction Unfreeze
    path("rack-transaction/<int:pk>/unfreeze-form/", rack_transaction_unfreeze_form, name="rack_transaction_unfreeze_form"),
    path("rack-transaction/<int:pk>/unfreeze/", rack_transaction_unfreeze, name="rack_transaction_unfreeze"),
]
