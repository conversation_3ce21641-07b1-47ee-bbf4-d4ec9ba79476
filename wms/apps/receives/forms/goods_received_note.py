from django import forms
from django.core.exceptions import ValidationError

from wms.apps.consignees.models import Consignee
from wms.apps.consignors.models import Consignor
from wms.apps.settings.models import UnitOfMeasure, Warehouse
from wms.apps.receives.models import GoodsReceivedNote, GoodsReceivedNoteItem
from wms.cores.forms.fields import CoreModelForm, CoreCharField
from wms.apps.inventories.models import Stock, Item
from wms.cores.forms.widget import CoreSelectWidget, CoreNumberWidget, CoreTextWidget, CoreCheckboxSelectMultipleWidget


class NoValidationChoiceField(forms.ChoiceField):
    """
    A ChoiceField that doesn't validate its value.
    Used for dynamic fields that are populated via JavaScript.
    """

    def validate(self, value):
        pass  # Skip validation


class GoodsReceivedNoteForm(CoreModelForm):
    """
    Form for creating and updating GoodsReceivedNote.
    """
    # Hidden fields to store IDs
    consignor_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    consignor = forms.CharField(
        required=True,
        widget=CoreSelectWidget(
            attrs={
                'class': 'goods-received-note-consignor',
                'data-api-url': '/api/consignors/',
            }
        ),
        help_text="Select a consignor first to enable consignee selection."
    )

    customer_reference = CoreCharField(
        label="Reference",
        required=False,
        widget=CoreTextWidget(
            attrs={
                'class': 'w-xs',
            }
        )
    )

    class Meta:
        model = GoodsReceivedNote
        fields = [
            "status",
            "consignor",
            "issued_by",
            "deliver_to",
            "arrival_datetime",
            "customer_reference",
            "remark",
        ]

    def __init__(self, *args, **kwargs):
        # To use self.warehouse_release_order for initial values, validation, etc.
        self.warehouse_release_order = kwargs.pop("warehouse_release_order", None)

        super().__init__(*args, **kwargs)

        self.fields["status"].disabled = True
        self.fields["status"].widget = forms.HiddenInput()
        self.fields["issued_by"].disabled = True
        self.fields["arrival_datetime"].label = 'Arrival Time'
        self.fields["remark"].widget.attrs.update({"cols": 70, "rows": 4})

        # Explicitly set the datetime input format
        self.fields['arrival_datetime'].input_formats = ['%Y-%m-%d %H:%M']

        if self.instance and self.instance.pk and self.instance.consignor:
            self.fields['consignor'].initial = self.instance.consignor.display_name
            self.initial['consignor_id'] = self.instance.consignor.pk

            self.fields["consignor"].widget.choices = [
                (self.instance.consignor.pk, self.instance.consignor.display_name)
            ]

        # --- Handle formset initial data (create case) for "Return" case from WRO ---
        if not self.instance or not self.instance.pk:
            initial = self.initial

            if 'consignor' in initial:
                consignor = initial.get('consignor')
                if hasattr(consignor, 'id'):
                    self.fields['consignor'].initial = str(consignor)
                    self.initial['consignor_id'] = consignor.id
                else:
                    self.fields['consignor'].initial = consignor

    def clean(self):
        cleaned_data = super().clean()
        consignor_id = cleaned_data.get('consignor_id')

        # If we don't have IDs but have names, try to get the IDs
        if not consignor_id and 'consignor' in cleaned_data:
            try:
                # Try to get consignor by name or ID
                consignor_name = cleaned_data.get('consignor')
                if consignor_name.isdigit():
                    consignor = Consignor.objects.filter(pk=consignor_name).first()
                else:
                    consignor = Consignor.objects.filter(display_name__iexact=consignor_name).first()
                    if not consignor:
                        # Try by code as fallback
                        consignor = Consignor.objects.filter(code__iexact=consignor_name).first()
                if consignor:
                    consignor_id = consignor.pk
                    cleaned_data['consignor_id'] = consignor_id
            except (Consignor.DoesNotExist, ValueError, AttributeError):
                pass

        # Get the actual model instances
        consignor = Consignor.objects.filter(pk=consignor_id).first() if consignor_id else None

        # Store the actual model instances in cleaned_data
        cleaned_data['consignor'] = consignor

        return cleaned_data


class GoodsReceivedNoteItemForm(CoreModelForm):
    """
    Form for GoodsReceivedNote Items.
    """
    item = NoValidationChoiceField(
        disabled=True,
        required=True,
        widget=CoreSelectWidget(
            attrs={
                'class': 'goods-received-note-item w-xl',
                'data-api-url': '/api/consignors/{consignor_id}/items/'
            }
        )
    )

    batch_no = NoValidationChoiceField(
        choices=[],
        required=False,
        widget=CoreSelectWidget(
            attrs={
                'class': 'goods-received-note-batch w-40',
                'disabled': 'disabled',
                'tags': 'true',  # enabled select2 dynamic option
            }
        )
    )

    expiry_date = NoValidationChoiceField(
        choices=[],
        required=False,
        widget=CoreSelectWidget(
            attrs={
                'class': 'goods-received-note-expiry w-40',
                'disabled': 'disabled',
                'tags': 'true',  # enabled select2 dynamic option
            }
        )
    )

    quantity = forms.DecimalField(
        required=True,
        widget=CoreNumberWidget(
            attrs={
                'class': 'goods-received-note-quantity w-25',
            },
        )
    )



    uom = forms.CharField(
        required=False,
        widget=CoreTextWidget(
            attrs={
                'class': 'goods-received-note-uom w-25',
                'disabled': 'disabled',
                'readonly': 'readonly'
            }
        )
    )

    item_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
    uom_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    # Hidden fields to store batch_no and expiry_date values
    batch_no_hidden = forms.CharField(widget=forms.HiddenInput(), required=False)
    expiry_date_hidden = forms.CharField(widget=forms.HiddenInput(), required=False)

    class Meta:
        model = GoodsReceivedNoteItem
        fields = [
            "item",
            "batch_no",
            "expiry_date",
            "quantity",
            "uom",
            "item_id",
            "uom_id",
            "batch_no_hidden",
            "expiry_date_hidden",
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # --- Handle instance (edit case) ---
        # If we have an instance with data, we need to populate the fields
        if self.instance and self.instance.pk:
            # If editing an existing goods received note item, we need to set the initial values
            if self.instance.item:
                # Set initial values for hidden fields
                self.initial['item_id'] = self.instance.item.id

                # Enable the item field and set its initial value
                self.fields['item'].disabled = False
                self.fields['item'].initial = self.instance.item.name

                # Set batch_no and expiry_date if available
                if self.instance.batch_no:
                    self.fields['batch_no'].initial = self.instance.batch_no
                    self.initial['batch_no_hidden'] = self.instance.batch_no

                if self.instance.expiry_date:
                    self.fields['expiry_date'].initial = self.instance.expiry_date
                    self.initial['expiry_date_hidden'] = self.instance.expiry_date.strftime(
                        '%Y-%m-%d') if self.instance.expiry_date else ''

        # --- Handle formset initial data (create case) ---
        if not self.instance or not self.instance.pk:
            initial = self.initial

            if 'item' in initial:
                item = initial.get('item')
                if hasattr(item, 'id'):
                    self.fields['item'].initial = str(item)
                    self.initial['item_id'] = item.id
                else:
                    self.fields['item'].initial = item

            if 'batch_no' in initial:
                self.fields['batch_no'].initial = initial['batch_no']
                self.initial['batch_hidden'] = initial['batch_no']

            if 'expiry_date' in initial:
                self.fields['expiry_date'].initial = initial['expiry_date']
                self.initial['expiry_hidden'] = initial['expiry_date']

            # if 'uom' in initial:
            #     uom = initial.get('uom')
            #     if hasattr(uom, 'id'):
            #         self.fields['uom'].initial = str(uom)
            #         self.initial['uom_id'] = uom.id
            #     else:
            #         self.fields['uom'].initial = uom

    def clean(self):
        """Main form cleaning method"""
        cleaned_data = super().clean()
        item_id = cleaned_data.get('item_id')
        uom_id = cleaned_data.get('uom_id')
        expiry_date = cleaned_data.get('expiry_date')

        def validate_item():
            """Validate item selection"""
            if not item_id:
                raise ValidationError("Item selection is required")

            try:
                return Item.objects.get(id=item_id)
            except Item.DoesNotExist:
                raise ValidationError("Invalid item selection")

        def validate_uom():
            """Validate UOM selection"""
            if not uom_id:
                raise ValidationError("UOM is required when item is selected")

            try:
                return UnitOfMeasure.objects.get(id=uom_id)
            except UnitOfMeasure.DoesNotExist:
                raise ValidationError("Invalid UOM selection")

        if expiry_date == 'N/A' or expiry_date == '':
            cleaned_data['expiry_date'] = None
            expiry_date = None

        # Validate UOM
        try:
            uom = validate_uom()
            cleaned_data['uom'] = uom
        except ValidationError as e:
            self.add_error('uom', str(e))
            raise

        # Validate item
        try:
            item = validate_item()
            cleaned_data['item'] = item
        except ValidationError as e:
            self.add_error('item', str(e))
            raise

        # Validate that UOM belongs to the selected item
        if item and uom and item.uom.id != uom.id:
            self.add_error('uom', f"The selected UOM ({uom}) does not match the item's UOM ({item.uom})")

        # Convert string date to date object if needed
        if isinstance(expiry_date, str) and expiry_date:
            try:
                from datetime import datetime
                expiry_date = datetime.strptime(expiry_date, '%Y-%m-%d').date()
            except ValueError:
                self.add_error('expiry_date', f"Invalid date format: {expiry_date}")
                return cleaned_data

        return cleaned_data
