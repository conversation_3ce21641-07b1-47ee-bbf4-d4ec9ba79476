from django import forms
from django.utils.translation import gettext_lazy as _

from wms.cores.forms.fields import CoreModelForm
from wms.cores.forms.widget import CoreSelectWidget, CoreNumberWidget, CoreTextAreaWidget
from wms.apps.receives.models import GoodsReceivedNoteDefectStockIn
from wms.cores.utils import uom_choices_symbol, format_decimal_values


class GoodsReceivedNoteDefectStockInForm(CoreModelForm):
    """Form to create GoodsReceivedNoteDefectStockIn for rejecting items."""

    class Meta:
        model = GoodsReceivedNoteDefectStockIn
        fields = [
            "reason",
            "approved_quantity",
            "remark",
        ]
        widgets = {
            "reason": CoreSelectWidget(attrs={"class": "w-full"}),
            "approved_quantity": CoreNumberWidget(attrs={"class": "w-full", "step": "0.000001"}),
            "remark": CoreTextAreaWidget(attrs={"rows": 3, "class": "w-full"}),
        }

    def __init__(self, *args, **kwargs):
        self.goods_received_note_item = kwargs.pop('goods_received_note_item', None)
        super().__init__(*args, **kwargs)

        if self.goods_received_note_item:
            # Set initial values based on the GRN item
            self.fields['approved_quantity'].widget.attrs['value'] = 0
            self.fields['approved_quantity'].initial = 0
            self.fields["approved_quantity"].label = _("Reject Quantity")
            self.fields["approved_quantity"].help_text = _("Enter positive quantity to reject items, or negative quantity to correct previous rejections.")

    def clean(self):
        cleaned_data = super().clean()
        approved_quantity = cleaned_data.get('approved_quantity')

        if approved_quantity.is_zero() is True:
            self.add_error('approved_quantity', _("Quantity cannot be 0."))
            raise forms.ValidationError(_("Quantity cannot be 0."))

        if self.goods_received_note_item:
            current_rejected = self.goods_received_note_item.get_rejected_quantity

            # If negative quantity, check that final rejected quantity won't go below 0
            if approved_quantity < 0:
                final_rejected_quantity = current_rejected + approved_quantity
                if final_rejected_quantity < 0:
                    self.add_error(
                        'approved_quantity',
                        _("Adjustment would result in negative rejected quantity. Current rejected: %(current)s, adjustment: %(adjustment)s") % {
                            'current': current_rejected,
                            'adjustment': approved_quantity
                        }
                    )
                    raise forms.ValidationError(_("Final rejected quantity cannot be negative."))

        return cleaned_data
