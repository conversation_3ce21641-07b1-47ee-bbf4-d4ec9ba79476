from django import forms
from django.db.models import Q
from django.utils.translation import gettext_lazy as _

from wms.cores.forms.fields import CoreModelForm, CoreModelChoiceField
from wms.cores.forms.widget import CoreNumberWidget, CoreTextAreaWidget, CoreSelectWidget, CoreSelectMultipleWidget
from wms.cores.utils import format_decimal_values

from wms.apps.receives.models import GoodsReceivedNote, GoodsReceivedNoteStockIn
from wms.apps.rackings.models import Rack, RackStorage, RackTransaction
from wms.apps.inventories.models import Stock
from decimal import Decimal


class GoodsReceivedNotePutAwayForm(CoreModelForm):
    """Main form for putting away received goods with multi-select rack selection."""

    # Multi-select field for rack locations
    racks = forms.ModelMultipleChoiceField(
        queryset=None,  # Will be set in __init__
        required=True,
        label=_("Rack Locations"),
        widget=CoreSelectMultipleWidget(attrs={
            "class": "put-away-racks w-full",
            "data-placeholder": _("Select rack locations..."),
        }),
        help_text=_("Select one or more rack locations where the items will be stored.")
    )

    # Quantity to put away to each selected rack
    quantity = forms.IntegerField(
        required=True,
        label=_("Quantity per Rack"),
        widget=CoreNumberWidget(attrs={
            "class": "w-full",
            "id": "id_quantity",
            "step": "1",
            "min": "1",
        }),
        help_text=_("Enter the quantity to put away to each selected rack location.")
    )

    # Remark field
    remark = forms.CharField(
        required=False,
        label=_("Remark"),
        widget=CoreTextAreaWidget(attrs={"rows": 3, "class": "w-full"}),
        help_text=_("Optional remarks for this put-away operation.")
    )

    class Meta:
        model = RackTransaction
        fields = []  # We don't use model fields directly, but need Meta for CoreModelForm

    def __init__(self, *args, **kwargs):
        self.goods_received_note_item = kwargs.pop('goods_received_note_item', None)
        # Remove any unexpected kwargs that CoreCreateView might pass
        kwargs.pop('request', None)
        kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        if self.goods_received_note_item:
            # Set up rack queryset for the warehouse
            warehouse = self.goods_received_note_item.goods_received_note.deliver_to
            available_racks = Rack.objects.filter(
                warehouse=warehouse,
                rack_type=Rack.RackType.PALLET
            ).order_by('full_name')

            self.fields['racks'].queryset = available_racks
            self.fields['racks'].label_from_instance = lambda obj: obj.full_name

            # Calculate available quantity to put away
            total_received = sum(
                stock_in.approved_quantity for stock_in in
                GoodsReceivedNoteStockIn.objects.filter(
                    goods_received_note_item=self.goods_received_note_item
                )
            )

            # Get or create stock for this item
            stock, created = Stock.objects.get_or_create(
                item=self.goods_received_note_item.item,
                batch_no=self.goods_received_note_item.batch_no,
                expiry_date=self.goods_received_note_item.expiry_date,
                warehouse=warehouse
            )

            # Calculate already put away quantity
            already_put_away = sum(
                rack_tx.quantity for rack_tx in
                RackTransaction.objects.filter(
                    type=RackTransaction.Type.GRN,
                    rackstorage__stock=stock
                )
            )

            available_to_put_away = total_received - already_put_away

            if available_to_put_away > 0:
                formatted_available_qty = format_decimal_values(available_to_put_away)
                self.fields['quantity'].widget.attrs['max'] = str(available_to_put_away)

    def clean_racks(self):
        racks = self.cleaned_data.get('racks')

        if not racks:
            raise forms.ValidationError(_("At least one rack location must be selected."))

        return racks

    def clean_quantity(self):
        quantity = self.cleaned_data.get('quantity')

        if quantity is None:
            raise forms.ValidationError(_("Quantity is required."))

        if quantity <= 0:
            raise forms.ValidationError(_("Quantity must be greater than 0."))

        return quantity

    def clean(self):
        cleaned_data = super().clean()

        # Additional validation can be added here if needed
        racks = cleaned_data.get('racks')
        quantity = cleaned_data.get('quantity')

        if racks and quantity and self.goods_received_note_item:
            # Calculate total quantity to be put away
            total_quantity_to_put_away = quantity * len(racks)

            # Calculate available quantity
            total_received = sum(
                stock_in.approved_quantity for stock_in in
                GoodsReceivedNoteStockIn.objects.filter(
                    goods_received_note_item=self.goods_received_note_item
                )
            )

            # Get stock for this item
            try:
                stock = Stock.objects.get(
                    item=self.goods_received_note_item.item,
                    batch_no=self.goods_received_note_item.batch_no,
                    expiry_date=self.goods_received_note_item.expiry_date,
                    warehouse=self.goods_received_note_item.goods_received_note.deliver_to
                )

                already_put_away = sum(
                    rack_tx.quantity for rack_tx in
                    RackTransaction.objects.filter(
                        type=RackTransaction.Type.GRN,
                        rackstorage__stock=stock
                    )
                )
            except Stock.DoesNotExist:
                already_put_away = 0

            available_to_put_away = total_received - already_put_away

            if total_quantity_to_put_away > available_to_put_away:
                raise forms.ValidationError(
                    _("Total quantity to put away (%(total)s = %(quantity)s × %(racks)s racks) exceeds available quantity (%(available)s).") % {
                        'total': total_quantity_to_put_away,
                        'quantity': quantity,
                        'racks': len(racks),
                        'available': available_to_put_away
                    }
                )

        return cleaned_data
