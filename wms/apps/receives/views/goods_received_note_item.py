import json
from decimal import Decimal, InvalidOperation

from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import get_object_or_404, render
from django.template.loader import render_to_string
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_POST
from django.urls import reverse

from wms.cores.views import CoreCreateView
from wms.apps.receives.forms.goods_received_note_item_receive import GoodsReceivedNoteStockInForm
from wms.apps.receives.forms.goods_received_note_item_add import GoodsReceivedNoteItemAddForm
from wms.apps.receives.forms.goods_received_note_item_reject import GoodsReceivedNoteDefectStockInForm
from wms.apps.receives.models import GoodsReceivedNoteItem, GoodsReceivedNote, GoodsReceivedNoteStockIn, GoodsReceivedNoteDefectStockIn
from wms.apps.settings.models import UnitOfMeasure
from wms.apps.inventories.models import Transaction, Item


class GoodsReceivedNoteItemStockInFormView(CoreCreateView):
    """
    Display and process the form for receiving a GRN item.
    """
    model = GoodsReceivedNoteStockIn
    form_class = GoodsReceivedNoteStockInForm
    template_name = 'receives/partials/goods_received_note_item_receive_form.html'
    success_url = 'receives:goods_received_notes:detail'

    def dispatch(self, request, *args, **kwargs):
        # Get the GRN item
        self.goods_received_note_item = get_object_or_404(GoodsReceivedNoteItem, pk=self.kwargs['pk'])

        allowed_statuses = [
            GoodsReceivedNote.Status.NEW,
            GoodsReceivedNote.Status.PARTIALLY_RECEIVED,
        ]

        # Check if the parent order is in 'NEW' status
        if self.goods_received_note_item.goods_received_note.status not in allowed_statuses:
            if self.request.htmx:
                headers = {
                    'HX-Trigger': json.dumps({
                        "closeModalEvent": None,
                        "showNotificationEvent": {
                            "message": "Receiving is only allowed for GRNs in 'New' status.",
                            "type": "error"
                        }
                    })
                }
                return HttpResponse(status=400, headers=headers)
            messages.error(request, _("Receiving is only allowed for GRNs in 'New' status."))
            # Return an empty response or a message indicating the restriction
            return HttpResponse(status=204)  # No content, or render a message template

        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['goods_received_note_item'] = self.goods_received_note_item
        return kwargs

    def form_valid(self, form):
        try:
            form.instance.goods_received_note_item = self.goods_received_note_item
            form.instance.item = self.goods_received_note_item.item
            form.instance.uom = self.goods_received_note_item.uom
            form.instance.batch_no = self.goods_received_note_item.batch_no or "N/A"
            form.instance.expiry_date = self.goods_received_note_item.expiry_date
            form.instance.remark = form.cleaned_data.get('remark')
            form.instance.approved_by = self.request.user
            form.instance.deliver_to = self.goods_received_note_item.goods_received_note.deliver_to
            form.instance.stock_in_datetime = timezone.now()
            form.instance.stock_in_channel = "goods_received_note"
            approved_quantity = Decimal(str(form.cleaned_data['approved_quantity']))
            form.instance.approved_quantity = approved_quantity

            response = super().form_valid(form)

            # Refresh the goods_received_note_item to get updated values, can't use refresh_from_db() due to prefetch_related
            self.goods_received_note_item = GoodsReceivedNoteItem.objects.get(pk=self.goods_received_note_item.pk)

            # Check if this is an HTMX request
            if self.request.headers.get('HX-Request'):
                # For HTMX requests, return a JSON response with updated HTML fragments
                # Prepare context for templates
                context = {'record': self.goods_received_note_item, 'request': self.request}

                # Use correct IDs that match the actual template IDs
                actions_id = f"goods-received-note-item-actions-{self.goods_received_note_item.pk}"
                percentage_id = f"goods-received-note-item-percentage-{self.goods_received_note_item.pk}"
                quantity_id = f"goods-received-note-item-quantity-{self.goods_received_note_item.pk}"
                received_quantity_id = f"goods-received-note-item-received-quantity-{self.goods_received_note_item.pk}"
                rejected_quantity_id = f"goods-received-note-item-rejected-quantity-{self.goods_received_note_item.pk}"
                running_number_id = f"goods-received-note-item-running-number-{self.goods_received_note_item.pk}"

                actions_html = render_to_string(
                    'receives/partials/goods_received_note_item_actions.html',
                    context,
                    request=self.request
                )
                percentage_html = render_to_string(
                    'receives/partials/goods_received_note_item_percentage.html',
                    context,
                    request=self.request
                )
                quantity_html = render_to_string(
                    'receives/partials/goods_received_note_item_quantity.html',
                    context,
                    request=self.request
                )
                received_quantity_html = render_to_string(
                    'receives/partials/goods_received_note_item_received_quantity.html',
                    context,
                    request=self.request
                )
                rejected_quantity_html = render_to_string(
                    'receives/partials/goods_received_note_item_reject_quantity.html',
                    context,
                    request=self.request
                )
                running_number_html = render_to_string(
                    'receives/partials/goods_received_note_item_running_number.html',
                    context,
                    request=self.request
                )

                # Return JSON response with all HTML fragments
                response_html = f"""
                <div id="{actions_id}" hx-swap-oob="true">{actions_html}</div>
                <div id="{percentage_id}" hx-swap-oob="true">{percentage_html}</div>
                <div id="{quantity_id}" hx-swap-oob="true">{quantity_html}</div>
                <div id="{received_quantity_id}" hx-swap-oob="true">{received_quantity_html}</div>
                <div id="{rejected_quantity_id}" hx-swap-oob="true">{rejected_quantity_html}</div>
                <div id="{running_number_id}" hx-swap-oob="true">{running_number_html}</div>
                <div></div>
                """

                trigger_data = {
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Item received successfully!",
                        "type": "success"
                    }
                }

                headers = {
                    'HX-Trigger': json.dumps(trigger_data)
                }
                response = HttpResponse(response_html, headers=headers)
                return response

            # For regular form submissions, return the standard redirect response
            return response

        except Exception as e:
            # If any exception occurs during processing, add it as a form error
            form.add_error(None, str(e))
            return self.form_invalid(form)

    def get_success_url(self):
        # Redirect to the GRN detail page
        return reverse(
            'receives:goods_received_notes:detail',
            kwargs={'pk': self.goods_received_note_item.goods_received_note.pk}
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        context.update({
            'goods_received_note_item': self.goods_received_note_item,
        })

        return context


class GoodsReceivedNoteItemRejectFormView(CoreCreateView):
    """
    Display and process the form for rejecting a GRN item.
    """
    model = GoodsReceivedNoteDefectStockIn
    form_class = GoodsReceivedNoteDefectStockInForm
    template_name = 'receives/partials/goods_received_note_item_reject_form.html'
    success_url = 'receives:goods_received_notes:detail'

    def dispatch(self, request, *args, **kwargs):
        # Get the GRN item
        self.goods_received_note_item = get_object_or_404(GoodsReceivedNoteItem, pk=self.kwargs['pk'])

        allowed_statuses = [
            GoodsReceivedNote.Status.NEW,
            GoodsReceivedNote.Status.PARTIALLY_RECEIVED,
        ]

        # Check if the parent order is in 'NEW' or 'PARTIALLY_RECEIVED' status
        if self.goods_received_note_item.goods_received_note.status not in allowed_statuses:
            if self.request.htmx:
                headers = {
                    'HX-Trigger': json.dumps({
                        "closeModalEvent": None,
                        "showNotificationEvent": {
                            "message": "Rejection is only allowed for GRNs in 'New' or 'Partially Received' status.",
                            "type": "error"
                        }
                    })
                }
                return HttpResponse(status=400, headers=headers)
            messages.error(request, _("Rejection is only allowed for GRNs in 'New' or 'Partially Received' status."))
            return HttpResponse(status=204)

        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['goods_received_note_item'] = self.goods_received_note_item
        return kwargs

    def form_valid(self, form):
        try:
            form.instance.goods_received_note_item = self.goods_received_note_item
            form.instance.item = self.goods_received_note_item.item
            form.instance.uom = self.goods_received_note_item.uom
            form.instance.batch_no = self.goods_received_note_item.batch_no or "N/A"
            form.instance.expiry_date = self.goods_received_note_item.expiry_date
            form.instance.remark = form.cleaned_data.get('remark')
            form.instance.approved_by = self.request.user
            form.instance.deliver_to = self.goods_received_note_item.goods_received_note.deliver_to
            form.instance.stock_in_datetime = timezone.now()
            form.instance.stock_in_channel = "goods_received_note"
            approved_quantity = Decimal(str(form.cleaned_data['approved_quantity']))
            form.instance.approved_quantity = approved_quantity

            response = super().form_valid(form)
            # Refresh the goods_received_note_item to get updated values, can't use refresh_from_db() due to prefetch_related
            self.goods_received_note_item = GoodsReceivedNoteItem.objects.get(pk=self.goods_received_note_item.pk)

            # Check if this is an HTMX request
            if self.request.headers.get('HX-Request'):
                # For HTMX requests, return a JSON response with updated HTML fragments

                # Prepare context for templates
                context = {'record': self.goods_received_note_item, 'request': self.request}

                # Use correct IDs that match the actual template IDs
                actions_id = f"goods-received-note-item-actions-{self.goods_received_note_item.pk}"
                percentage_id = f"goods-received-note-item-percentage-{self.goods_received_note_item.pk}"
                quantity_id = f"goods-received-note-item-quantity-{self.goods_received_note_item.pk}"
                received_quantity_id = f"goods-received-note-item-received-quantity-{self.goods_received_note_item.pk}"
                rejected_quantity_id = f"goods-received-note-item-rejected-quantity-{self.goods_received_note_item.pk}"
                running_number_id = f"goods-received-note-item-running-number-{self.goods_received_note_item.pk}"

                actions_html = render_to_string(
                    'receives/partials/goods_received_note_item_actions.html',
                    context,
                    request=self.request
                )
                percentage_html = render_to_string(
                    'receives/partials/goods_received_note_item_percentage.html',
                    context,
                    request=self.request
                )
                quantity_html = render_to_string(
                    'receives/partials/goods_received_note_item_quantity.html',
                    context,
                    request=self.request
                )
                received_quantity_html = render_to_string(
                    'receives/partials/goods_received_note_item_received_quantity.html',
                    context,
                    request=self.request
                )
                rejected_quantity_html = render_to_string(
                    'receives/partials/goods_received_note_item_reject_quantity.html',
                    context,
                    request=self.request
                )
                running_number_html = render_to_string(
                    'receives/partials/goods_received_note_item_running_number.html',
                    context,
                    request=self.request
                )

                # Return JSON response with all HTML fragments
                response_html = f"""
                <div id="{actions_id}" hx-swap-oob="true">{actions_html}</div>
                <div id="{percentage_id}" hx-swap-oob="true">{percentage_html}</div>
                <div id="{quantity_id}" hx-swap-oob="true">{quantity_html}</div>
                <div id="{received_quantity_id}" hx-swap-oob="true">{received_quantity_html}</div>
                <div id="{rejected_quantity_id}" hx-swap-oob="true">{rejected_quantity_html}</div>
                <div id="{running_number_id}" hx-swap-oob="true">{running_number_html}</div>
                <div></div>
                """

                trigger_data = {
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Item rejected successfully!",
                        "type": "success"
                    }
                }

                headers = {
                    'HX-Trigger': json.dumps(trigger_data)
                }
                response = HttpResponse(response_html, headers=headers)
                return response

            # For regular form submissions, return the standard redirect response
            return response

        except Exception as e:
            # If any exception occurs during processing, add it as a form error
            form.add_error(None, str(e))
            return self.form_invalid(form)

    def get_success_url(self):
        # Redirect to the GRN detail page
        return reverse(
            'receives:goods_received_notes:detail',
            kwargs={'pk': self.goods_received_note_item.goods_received_note.pk}
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        context.update({
            'goods_received_note_item': self.goods_received_note_item,
        })

        return context


def goods_received_note_item_delete_form(request, pk):
    """
    Display the confirmation form for deleting a GRN item.
    """
    goods_received_note_item = get_object_or_404(GoodsReceivedNoteItem, pk=pk)

    # Check if the item has any receives
    if goods_received_note_item.get_received_quantity > 0:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Deletion is only allowed for items with no receives.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request, _("Deletion is only allowed for items with no receives."))
        # Return an empty response or a message indicating the restriction
        return HttpResponse(status=204)  # No content, or render a message template

    # Check if this is the last item in the GRN
    item_count = goods_received_note_item.goods_received_note.goodsreceivednoteitem_set.count()
    if item_count <= 1:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Cannot delete the last item in a GRN.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request, _("Cannot delete the last item in a GRN."))
        # Return an empty response or a message indicating the restriction
        return HttpResponse(status=204)  # No content, or render a message template

    context = {
        'goods_received_note_item': goods_received_note_item,
        'request': request,
    }

    return render(request, 'receives/partials/goods_received_note_item_delete_form.html', context)


@require_POST
def goods_received_note_item_delete(request, pk):
    """
    Delete a GRN item.
    """
    goods_received_note_item = get_object_or_404(GoodsReceivedNoteItem, pk=pk)

    # Check if the GRN is in New status
    if goods_received_note_item.goods_received_note.status != 'New':
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Deletion is only allowed for GRNs in 'New' status.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request, _("Deletion is only allowed for GRNs in 'New' status."))
        # Return an empty response or a message indicating the restriction
        # Use 400 Bad Request as the action is invalid at this state
        return HttpResponse(status=400, content="Deletion not allowed for this order status.")

    # Check if the item has any receives
    if goods_received_note_item.get_received_quantity > 0:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Deletion is only allowed for items with no receives.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request, _("Deletion is only allowed for items with no receives."))
        # Return an empty response or a message indicating the restriction
        # Use 400 Bad Request as the action is invalid at this state
        return HttpResponse(status=400, content="Deletion not allowed for items with receives.")

    # Check if this is the last item in the GRN
    item_count = goods_received_note_item.goods_received_note.goodsreceivednoteitem_set.count()
    if item_count <= 1:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Cannot delete the last item in a GRN.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request, _("Cannot delete the last item in a GRN."))
        # Return an empty response or a message indicating the restriction
        # Use 400 Bad Request as the action is invalid at this state
        return HttpResponse(status=400, content="Cannot delete the last item in a GRN.")

    try:
        goods_received_note_item.delete()
        response = HttpResponse(status=200)
        if request.headers.get('HX-Request'):
            trigger_data = {
                "closeModalEvent": None,
                "showNotificationEvent": {
                    "message": "Item deleted successfully!",
                    "type": "success"
                }
            }
            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            response = HttpResponse(status=200, headers=headers)
        return response
    except Exception as e:
        messages.error(request, _("Failed to delete GRN item: %(error)s") % {'error': str(e)})
        # Return an error response for HTMX
        return HttpResponse(status=500, content=f"Error deleting item: {str(e)}")



class GoodsReceivedNoteItemAddView(CoreCreateView):
    """
    Display and process the form for adding a new GRN item.
    """
    model = GoodsReceivedNoteItem
    form_class = GoodsReceivedNoteItemAddForm
    template_name = 'receives/partials/goods_received_note_item_add_form.html'
    success_url = 'receives:goods_received_notes:detail'

    def dispatch(self, request, *args, **kwargs):
        # Get the GRN
        self.goods_received_note = get_object_or_404(GoodsReceivedNote, pk=self.kwargs['pk'])

        allowed_statuses = [
            GoodsReceivedNote.Status.NEW,
            GoodsReceivedNote.Status.PARTIALLY_RECEIVED,
        ]

        # Check if the GRN is in 'NEW' status
        if self.goods_received_note.status not in allowed_statuses:
            if self.request.htmx:
                headers = {
                    'HX-Trigger': json.dumps({
                        "closeModalEvent": None,
                        "showNotificationEvent": {
                            "message": "Items can only be added to GRNs in 'New' or 'Partially Received' status.",
                            "type": "error"
                        }
                    })
                }
                return HttpResponse(status=400, headers=headers)
            messages.error(request, _("Items can only be added to GRNs in 'New' or 'Partially Received' status."))
            return HttpResponse(status=400, content="GRN not eligible for adding items.")

        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['goods_received_note'] = self.goods_received_note
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['goods_received_note'] = self.goods_received_note
        return context

    def form_valid(self, form):
        try:
            # Get the item and UOM from the form
            item_id = form.cleaned_data.get('item_id')
            uom_id = form.cleaned_data.get('uom_id')

            # Get the actual objects
            item = get_object_or_404(Item, pk=item_id)
            uom = get_object_or_404(UnitOfMeasure, pk=uom_id)

            # Create the GRN item
            goods_received_note_item = form.save(commit=False)
            goods_received_note_item.item = item
            goods_received_note_item.uom = uom
            goods_received_note_item.goods_received_note = self.goods_received_note
            goods_received_note_item.created_by = self.request.user
            goods_received_note_item.batch_no = form.cleaned_data.get('batch_no')
            goods_received_note_item.expiry_date = form.cleaned_data.get('expiry_date')
            goods_received_note_item.save()

            # For HTMX requests, return a success response and trigger refreshes
            if self.request.headers.get('HX-Request'):
                # Get the detail URL for the GRN
                detail_url = reverse('receives:goods_received_notes:detail', kwargs={'pk': self.goods_received_note.pk})

                # Set up HTMX triggers for success notification, modal close, and tab refresh
                trigger_data = {
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Item added successfully!",
                        "type": "success"
                    },
                    "refreshTabContent": {
                        "url": detail_url
                    }
                }

                headers = {
                    'HX-Trigger': json.dumps(trigger_data)
                }

                # Return an empty response with headers - no need for row_html since we're refreshing the whole tab
                return HttpResponse('', headers=headers, status=200)

            # For regular form submissions, return the standard redirect response
            return super().form_valid(form)

        except Exception as e:
            # If any exception occurs during processing, add it as a form error
            form.add_error(None, str(e))
            return self.form_invalid(form)

    def form_invalid(self, form):
        # For HTMX requests, return the form with errors
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))

        # For regular form submissions, return the standard response
        return super().form_invalid(form)
