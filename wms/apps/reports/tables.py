from django.utils.translation import gettext_lazy as _

# import django_tables2 as tables

# from wms.cores.models import DISPLAY_EMPTY_VALUE
# from wms.cores.tables import TABLE_ATTRS_CLASS

# from wms.apps.inventories.models import Stock
# from wms.apps.receives.models import GoodsReceivedNote
# from wms.apps.releases.models import WarehouseReleaseOrder

import django_tables2 as tables
# from django.urls import reverse, NoReverseMatch

from wms.apps.inventories.models import Stock, Transaction
from wms.apps.receives.models import GoodsReceivedNote
from wms.apps.releases.models import WarehouseReleaseOrder


class StockReportTable(tables.Table):
    """Table used on report list page."""

    section_title = "Stock Report Lists"
    section_name = "Stock Report"

    transaction_system_quantity = tables.Column(verbose_name=_("Transaction Qty"), accessor="db_balance")

    class Meta:
        model = Stock
        order_by = "warehouse"
        template_name = "tables/table_htmx.html"
        fields = [
            "warehouse",
            "item__name",
            "item__code",
            "item__brand",
            "batch_no",
            "transaction_system_quantity",
        ]
        sequence = [
            "warehouse",
            "item__name",
            "item__code",
            "item__brand",
            "batch_no",
            "transaction_system_quantity",
        ]

    def render_transaction_system_quantity(self, value, record):
        return f"{value} {record.item.uom.symbol}"


class GRNReportTable(tables.Table):
    section_title = "GRN Report Lists"
    section_name = "GRN Report"

    deliver_to = tables.Column(
        verbose_name=_("Warehouse"),
    )
    arrival_datetime = tables.DateTimeColumn(
        verbose_name=_("Arrival Date"),
        accessor="arrival_datetime",
        format="Y-m-d",
    )
    system_number = tables.Column(verbose_name=_("System Number"))
    # *NOTE: system number href should use bottom ones. But GRN detailview havent ready yet.
    # system_number = tables.LinkColumn("receives:good_received_note:panel", args=[tables.utils.A("pk")])

    class Meta:
        model = GoodsReceivedNote
        order_by = 'system_number'
        template_name = "tables/table_htmx.html"
        fields = [
            "system_number",
            "consignor",
            "deliver_to",
            "customer_reference",
            "arrival_datetime",
            "status",
        ]
        sequence = [
            "system_number",
            "consignor",
            "deliver_to",
            "customer_reference",
            "arrival_datetime",
            "status",
        ]

class WROReportTable(tables.Table):
    section_title = "WRO Report Lists"
    section_name = "WRO Report"

    release_datetime = tables.DateTimeColumn(
        verbose_name=_("Arrival Date"),
        accessor="release_datetime",
        format="Y-m-d",
    )
    system_number = tables.LinkColumn("releases:orders:panel", args=[tables.utils.A("pk")])

    class Meta:
        model = WarehouseReleaseOrder
        order_by = 'system_number'
        template_name = "tables/table_htmx.html"
        fields = [
            "system_number",
            "deliveryorder__system_number",
            "consignee__consignor",
            "warehouses",
            "customer_reference",
            "release_datetime",
            "status",
        ]
        sequence = [
            "system_number",
            "deliveryorder__system_number",
            "consignee__consignor",
            "warehouses",
            "customer_reference",
            "release_datetime",
            "status",
        ]


######################################################################################################
# OLD CODE, to be remove below.
######################################################################################################

# class GRNReportDataTables(tables.Table):
#     """Table used on GRN report list page."""

#     deliver_to = tables.Column(
#         verbose_name=_("Warehouse"),
#     )
#     arrival_datetime = tables.DateTimeColumn(
#         verbose_name=_("Arrival Date"),
#         accessor="arrival_datetime",
#         format="Y-m-d",
#     )
#     numbering = tables.Column(verbose_name=_("Numbering"), linkify=True)

#     class Meta:
#         model = GoodsReceivedNote
#         order_by = "deliver_to"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {"class": TABLE_ATTRS_CLASS, "id": "report_datatables", "style": "display: none;"}
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "numbering",
#             "consignor",
#             "deliver_to",
#             "customer_reference",
#             "arrival_datetime",
#             "status",
#         ]
#         sequence = [
#             "numbering",
#             "consignor",
#             "deliver_to",
#             "customer_reference",
#             "arrival_datetime",
#             "status",
#         ]

#     def render_status(self, value, record):
#         """Return nice html label display for status."""
#         return record.html_status_display


# class WROReportDataTables(tables.Table):
#     """Table used on WRO report list page."""

#     numbering = tables.Column(verbose_name=_("Numbering"), linkify=True)
#     deliveryorder__numbering = tables.Column(verbose_name=_("DO No."))
#     release_datetime = tables.DateTimeColumn(
#         verbose_name=_("Release Date"),
#         accessor="release_datetime",
#         format="Y-m-d",
#     )

#     class Meta:
#         model = WarehouseReleaseOrder
#         order_by = "numbering"  # Must follow the first row display in the table
#         default = DISPLAY_EMPTY_VALUE
#         template_name = "partials/tables/_datatables_server_side.html"
#         attrs = {"class": TABLE_ATTRS_CLASS, "id": "report_datatables", "style": "display: none;"}
#         row_attrs = {"data-id": lambda record: record.pk}

#         fields = [
#             "numbering",
#             "deliveryorder__numbering",
#             "customer_reference",
#             "consignee__consignor",
#             "warehouses",
#             "release_datetime",
#             "status",
#         ]
#         sequence = [
#             "numbering",
#             "deliveryorder__numbering",
#             "customer_reference",
#             "consignee__consignor",
#             "warehouses",
#             "release_datetime",
#             "status",
#         ]

#     def render_status(self, value, record: WarehouseReleaseOrder):
#         """Return nice html label display for status."""
#         return record.html_status_display

#     def render_warehouses(self, value, record: WarehouseReleaseOrder) -> str:
#         return ", ".join([warehouse.name for warehouse in record.warehouses.all()])
