import json
from decimal import Decimal
from django.contrib import messages
from django.core.exceptions import ValidationError
from django.db import transaction
from django.db.models import Q, Sum
from django.http import HttpResponse
from django.shortcuts import get_object_or_404, render, redirect
from django.urls import reverse
from django.utils.translation import gettext_lazy as _
from django.views.generic import FormView, TemplateView
from django.views.decorators.http import require_POST

from django_tables2 import RequestConfig

from wms.cores.mixins import FormKwargsRequestMixin

from wms.apps.pickings.forms.picking_list_add_wro import PickingListAddReleaseOrderForm
from wms.apps.pickings.models import PickingList
from wms.apps.pickings.tables.picking_list import PickingListSelectReleaseOrderTable
from wms.apps.releases.models import WarehouseReleaseOrder, WarehouseReleaseOrderItem
from wms.apps.rackings.models import RackTransaction


@require_POST
def picking_list_assign_rack(request, pk):
    """
    Assign racks for picking list's involved WRO/DO.
    """
    picking_list = get_object_or_404(PickingList, pk=pk)

    # Check if the picking list is in a status that allows to assign racking
    allowed_statuses = [
        PickingList.Status.NEW,
        PickingList.Status.PROCESSING
    ]

    if picking_list.status not in allowed_statuses:
        messages.error(request, _("Only Picking Lists in 'New' or 'Processing' status can be assign racking."))
        return HttpResponse(status=400, content="Picking list not eligible for assign racking action.")

    try:
        involved_wro_pk_list = list(picking_list.warehousereleaseorder_set.all().values_list("pk", flat=True))
        involved_wro_item_qs = WarehouseReleaseOrderItem.objects.filter(release_order__pk__in=involved_wro_pk_list)

        involved_stock_dict = {}
        #######################################################
        # *NOTE*: expected structure
        #######################################################
        # involved_stock_dict = {
        #     pk: {
        #         "warehouse": WarehouseObj,
        #         "item": ItemObj,
        #         "batch_no": str,
        #         "expiry_date": str,
        #         "consolidated_available_quantity": decimal
        #         "unassigned_wro_item_list": list [] | hold wro_item that have not assigned rack yet.
        #     },
        #     123: {
        #         "warehouse": 3PL2,
        #         "item": AP00258,
        #         "batch_no": "BN001",
        #         "expiry_date": "23/4/2028",
        #         "consolidated_available_quantity": 543.00
        #         "unassigned_wro_item_list": [wroitem_1, wroitem_2]
        #     }
        # }
        #######################################################

        all_picking_list_item_assigned_bool = True

        # main purpose is to find out what is the current state of consolidated_available_quantity needed for each stock
        for wro_item in involved_wro_item_qs:
            if wro_item.validate_wro_item_rack_assigned() is False:
                all_picking_list_item_assigned_bool = False

                expected_stock = wro_item.get_stock(release_from=picking_list.release_from)
                if expected_stock:
                    if expected_stock.pk in involved_stock_dict:
                        involved_stock_dict[expected_stock.pk]["consolidated_available_quantity"] += wro_item.quantity
                        involved_stock_dict[expected_stock.pk]["unassigned_wro_item_list"].append(wro_item)
                    else:
                        involved_stock_dict[expected_stock.pk] = {
                            "warehouse": expected_stock.warehouse,
                            "item": expected_stock.item,
                            "batch_no": expected_stock.batch_no,
                            "expiry_date": expected_stock.expiry_date,
                            "consolidated_available_quantity": wro_item.quantity,
                            "unassigned_wro_item_list": [wro_item],
                        }
            # wro_item.validate_wro_item_rack_assigned() is True, double check if the "consolidated_available_quantity" is exact match with the PickingListItem's consolidated_quantity, otherwise involved_stock_dict should consist of the to be assign info
            else:
                expected_stock = wro_item.get_stock(release_from=picking_list.release_from)
                if expected_stock:
                    rack_transaction_consolidated_qty = RackTransaction.objects.filter(
                        warehouse_release_order_item=wro_item,
                        is_freezed=False,
                    ).aggregate(Sum("quantity")).get("quantity__sum", Decimal("0")) or Decimal("0")

                    # E.G:
                    # - wro_item.quantity = 1300
                    # - rack_transaction_consolidated_qty = 1000
                    # - although the wroitem's is_rack_assigned=True, but it's not fully assign the assignment yet.
                    if wro_item.quantity != abs(rack_transaction_consolidated_qty):
                        all_picking_list_item_assigned_bool = False

                        if expected_stock.pk in involved_stock_dict:
                            involved_stock_dict[expected_stock.pk][
                                "consolidated_available_quantity"] += wro_item.quantity
                            involved_stock_dict[expected_stock.pk]["unassigned_wro_item_list"].append(wro_item)
                        else:
                            involved_stock_dict[expected_stock.pk] = {
                                "warehouse": expected_stock.warehouse,
                                "item": expected_stock.item,
                                "batch_no": expected_stock.batch_no,
                                "expiry_date": expected_stock.expiry_date,
                                "consolidated_available_quantity": wro_item.quantity,
                                "unassigned_wro_item_list": [wro_item],
                            }

        if all_picking_list_item_assigned_bool is True:
            if request.headers.get('HX-Request'):
                # Get the URL for the release order list view
                release_order_list_url = reverse('pickings:picking_lists:detail', kwargs={'pk': pk})
                trigger_data = {
                    "showNotificationEvent": {
                        "message": "All picking list's Item already assigned, nothing to assign anymore.",
                        "type": "error"
                    },
                    "refreshTabContent": {
                        "url": release_order_list_url
                    },
                }

                headers = {
                    'HX-Trigger': json.dumps(trigger_data)
                }
                return HttpResponse(status=200, headers=headers)

            # For non-HTMX requests, redirect back to the detail page with success message
            messages.success(request, _("All picking list's Item already assigned, nothing to assign anymore."))
            return HttpResponse(status=200)

        # after having the accurate involved_stock_dict that knows which stock still haven fully assign rack yet,
        # based on the current consolidated_quantity from picking_list_item,
        # check if the remaining unassigned wro_item's desired stock whether is it enough available quantity to fulfil
        picking_list_item_qs = picking_list.picking_list_items.all()

        with transaction.atomic():
            for picking_list_item in picking_list_item_qs:
                if picking_list_item.stock.pk in involved_stock_dict:
                    available_rack_transaction = 0

                    wro_item_qs = WarehouseReleaseOrderItem.objects.filter(
                        release_order__picking_list=picking_list_item.picking_list,
                        item=picking_list_item.stock.item,
                        expiry_date=picking_list_item.stock.expiry_date,
                        batch_no=picking_list_item.stock.batch_no,
                    )

                    if wro_item_qs.exists():
                        wro_item_pk_list = list(wro_item_qs.values_list("pk", flat=True))
                        rack_transaction_consolidated_qty = RackTransaction.objects.filter(
                            warehouse_release_order_item__in=wro_item_pk_list,
                            is_freezed=False,
                        ).aggregate(Sum("quantity")).get("quantity__sum", Decimal("0")) or Decimal("0")
                    else:
                        rack_transaction_consolidated_qty = 0

                    unassigned_consolidated_quantity = (
                        picking_list_item.consolidated_quantity
                        - abs(rack_transaction_consolidated_qty)
                    )

                    available_rack_transaction = RackTransaction.objects.balance_by_stock(
                        stock=picking_list_item.stock,
                        balance_type="available",
                    )

                    if unassigned_consolidated_quantity <= available_rack_transaction:
                        # Process each WRO item only once for this picking list item
                        for wro_item in involved_stock_dict[picking_list_item.stock.pk]["unassigned_wro_item_list"]:
                            wro_item_rack_transaction_consolidated_qty = RackTransaction.objects.filter(
                                warehouse_release_order_item=wro_item,
                                is_freezed=False,
                            ).aggregate(Sum("quantity")).get("quantity__sum", Decimal("0")) or Decimal("0")

                            qty_needed_to_assign = wro_item.quantity - abs(wro_item_rack_transaction_consolidated_qty)

                            wro_item.assign_available_rack(
                                stock=picking_list_item.stock,
                                full_quantity_needed_to_assign=qty_needed_to_assign
                            )
                    else:
                        raise ValidationError(
                            _(f"{picking_list_item} do not have enough available rack's quantity to be assign rack.")
                        )

        # For HTMX requests, return success response
        if request.headers.get('HX-Request'):
            # Get the URL for the release order list view
            release_order_list_url = reverse('pickings:picking_lists:detail', kwargs={'pk': pk})
            trigger_data = {
                "showNotificationEvent": {
                    "message": "Picking list assigned Rack successfully!",
                    "type": "success"
                },
                "refreshTabContent": {
                    "url": release_order_list_url
                },
            }

            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            return HttpResponse(status=200, headers=headers)

        # For non-HTMX requests, redirect back to the detail page with success message
        messages.success(request, _("Picking list assigned Rack successfully."))
        return HttpResponse(status=200)

    except Exception as e:
        # If an error occurs, return an error response
        if request.headers.get('HX-Request'):
            trigger_data = {
                "showNotificationEvent": {
                    "message":  _("Failed to assign Rack on picking list: %(error)s") % {'error': str(e)},
                    "type": "error"
                }
            }
            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            return HttpResponse(status=500, headers=headers)

        messages.error(request, _("Failed to assign Rack on picking list: %(error)s") % {'error': str(e)})
        return HttpResponse(status=500, content=f"Error assign Rack to picking list: {str(e)}")

