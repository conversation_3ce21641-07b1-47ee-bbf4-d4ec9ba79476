from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from decimal import Decimal

from wms.cores.utils import normalize_decimal
from wms.apps.rackings.models import RackStorage, RackTransaction
from wms.apps.releases.models import WarehouseReleaseOrderItem
from wms.apps.inventories.models import Stock


class ReleaseOrderItemRackReassignForm(forms.Form):
    """
    Form for reassigning a rack to a release order item.
    """
    from_rack_storage = forms.ModelChoiceField(
        queryset=RackStorage.objects.none(),
        label=_("From Rack"),
        required=True,
        widget=forms.Select(attrs={'class': 'django-select2 w-full'})
    )

    to_rack_storage = forms.ModelChoiceField(
        queryset=RackStorage.objects.none(),
        label=_("To Rack"),
        required=True,
        widget=forms.Select(attrs={'class': 'django-select2 w-full'})
    )

    quantity = forms.DecimalField(
        label=_("Quantity to Reassign"),
        required=True,
        min_value=0.000001,
        widget=forms.NumberInput(attrs={'class': 'w-full', 'step': '0.000001'})
    )

    def __init__(self, *args, release_order_item=None, **kwargs):
        super().__init__(*args, **kwargs)
        self.release_order_item = release_order_item

        if release_order_item:
            # Get the stock for this release order item
            stock_filter = {
                'item': release_order_item.item,
            }

            # Add batch_no if specified
            if release_order_item.batch_no:
                stock_filter['batch_no'] = release_order_item.batch_no

            # Add expiry_date if specified
            if release_order_item.expiry_date:
                stock_filter['expiry_date'] = release_order_item.expiry_date

            # Get the stock
            stock = Stock.objects.filter(**stock_filter).first()

            if stock:
                # Get rack storages with reserved transactions for this release order item
                reserved_rack_storages = []
                reserved_choices = []

                # Find all rack transactions that are reserved for this release order item
                reserved_transactions = RackTransaction.objects.filter(

                    warehouse_release_order_item=release_order_item,
                    is_reserved=True,
                    is_freezed=False
                )

                for transaction in reserved_transactions:
                    rack_storage = transaction.rackstorage
                    reserved_rack_storages.append(rack_storage.pk)
                    # Use absolute value of quantity since it's stored as negative
                    label = f"{rack_storage.rack.full_name} ({abs(normalize_decimal(transaction.quantity))})"
                    reserved_choices.append((rack_storage.pk, label))

                self.fields['from_rack_storage'].queryset = RackStorage.objects.filter(
                    pk__in=reserved_rack_storages
                ).order_by('rack__full_name')

                self.fields['from_rack_storage'].choices = reserved_choices

                # Set initial quantity to the full reserved amount
                if reserved_transactions.exists():
                    first_transaction = reserved_transactions.first()
                    self.fields['quantity'].initial = abs(normalize_decimal(first_transaction.quantity))

                # Get available rack storages for this stock (for destination)
                rack_storages = RackStorage.objects.filter(
                    stock=stock,
                ).order_by('rack__full_name')

                # Filter to only include rack storages with available quantity
                available_rack_storages = []
                for rack_storage in rack_storages:
                    available_quantity = rack_storage.get_current_available_rack_transaction_balance
                    if available_quantity > 0:
                        available_rack_storages.append(rack_storage.pk)

                self.fields['to_rack_storage'].queryset = RackStorage.objects.filter(
                    pk__in=available_rack_storages
                ).order_by('rack__full_name')

                # Add available quantity as a label suffix
                choices = []
                for rack_storage in self.fields['to_rack_storage'].queryset:
                    available_quantity = rack_storage.get_current_available_rack_transaction_balance
                    label = f"{rack_storage.rack.full_name} ({available_quantity})"
                    choices.append((rack_storage.pk, label))

                self.fields['to_rack_storage'].choices = choices

    def clean(self):
        cleaned_data = super().clean()
        from_rack_storage = cleaned_data.get('from_rack_storage')
        to_rack_storage = cleaned_data.get('to_rack_storage')
        quantity = cleaned_data.get('quantity')

        # Validate that from_rack_storage and to_rack_storage are not the same
        if from_rack_storage and to_rack_storage and from_rack_storage == to_rack_storage:
            raise ValidationError({
                'to_rack_storage': _("Cannot select the same rack for both source and destination")
            })

        if from_rack_storage and quantity:
            # Find the reserved transaction for this rack storage
            reserved_transaction = RackTransaction.objects.filter(
                warehouse_release_order_item=self.release_order_item,
                rackstorage=from_rack_storage,
                is_reserved=True,
                is_freezed=False
            ).first()

            if reserved_transaction:
                # Check if the quantity to reassign is not greater than the reserved quantity
                reserved_quantity = abs(reserved_transaction.quantity)
                if quantity > reserved_quantity:
                    raise ValidationError({
                        'quantity': _("Cannot reassign more than the reserved quantity ({})").format(reserved_quantity)
                    })
            else:
                raise ValidationError({
                    'from_rack_storage': _("No reserved transaction found for this rack storage")
                })

        # Validate that the destination rack has enough available quantity
        if to_rack_storage and quantity:
            available_quantity = to_rack_storage.get_current_available_rack_transaction_balance
            if quantity > available_quantity:
                raise ValidationError({
                    'quantity': _("Cannot reassign more than the available quantity in the destination rack ({}). Please select a different rack or reduce the quantity.").format(available_quantity)
                })

            # Validate that the quantity being reassigned doesn't exceed the total quantity of the WarehouseReleaseOrderItem
            if self.release_order_item and quantity > self.release_order_item.expected_quantity:
                raise ValidationError({
                    'quantity': _("Cannot reassign more than the total expected quantity of the release order item ({}). Please reduce the quantity.").format(self.release_order_item.expected_quantity)
                })

        return cleaned_data
