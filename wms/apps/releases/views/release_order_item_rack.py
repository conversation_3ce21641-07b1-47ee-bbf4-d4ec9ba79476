import json
from decimal import Decimal

from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import get_object_or_404, render, redirect
from django.template.loader import render_to_string
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_POST
from django.urls import reverse

from wms.apps.releases.forms.release_order_item_rack_reassign import ReleaseOrderItemRackReassignForm
from wms.apps.releases.models import WarehouseReleaseOrderItem, WarehouseReleaseOrder
from wms.apps.inventories.models import Stock
from wms.apps.rackings.models import RackStorage, RackTransaction


def release_order_item_rack_reassign_form(request, pk):
    """
    Display the form for reassigning a rack to a release order item.
    """
    release_order_item = get_object_or_404(WarehouseReleaseOrderItem, pk=pk)

    # Check if the item is in a status that allows rack reassignment
    if release_order_item.release_order.status not in [WarehouseReleaseOrder.Status.NEW,
                                                      WarehouseReleaseOrder.Status.PROCESSING]:
        messages.error(request, _("Rack reassignment is only allowed for Release Orders in 'New' or 'Processing' status."))
        return HttpResponse(status=400, content="Release order not eligible for rack reassignment.")

    # Check if the item is in a status that allows rack reassignment
    if release_order_item.status not in [WarehouseReleaseOrderItem.Status.NEW,
                                         WarehouseReleaseOrderItem.Status.PROCESSING]:
        messages.error(request, _("Rack reassignment is only allowed for items in 'New' or 'Processing' status."))
        return HttpResponse(status=400, content="Item not eligible for rack reassignment.")

    # Initialize the form with the current item
    form = ReleaseOrderItemRackReassignForm(release_order_item=release_order_item)

    context = {
        'record': release_order_item,
        'form': form,
        'request': request,
    }

    return render(request, 'releases/partials/release_order_item_rack_reassign_form.html', context)


@require_POST
def release_order_item_rack_reassign(request, pk):
    """
    Process the reassignment of a rack to a release order item.
    """
    release_order_item = get_object_or_404(WarehouseReleaseOrderItem, pk=pk)

    # Check if the item is in a status that allows rack reassignment
    if release_order_item.release_order.status not in [WarehouseReleaseOrder.Status.NEW,
                                                      WarehouseReleaseOrder.Status.PROCESSING]:
        messages.error(request, _("Rack reassignment is only allowed for Release Orders in 'New' or 'Processing' status."))
        return HttpResponse(status=400, content="Release order not eligible for rack reassignment.")

    # Check if the item is in a status that allows rack reassignment
    if release_order_item.status not in [WarehouseReleaseOrderItem.Status.NEW,
                                         WarehouseReleaseOrderItem.Status.PROCESSING]:
        messages.error(request, _("Rack reassignment is only allowed for items in 'New' or 'Processing' status."))
        return HttpResponse(status=400, content="Item not eligible for rack reassignment.")

    # Process the form
    try:
        form = ReleaseOrderItemRackReassignForm(request.POST, release_order_item=release_order_item)

        if form.is_valid():
            # Get the form data
            from_rack_storage = form.cleaned_data['from_rack_storage']
            to_rack_storage = form.cleaned_data['to_rack_storage']
            quantity_to_reassign = form.cleaned_data['quantity']

            # Get the stock for this release order item
            stock_filter = {
                'item': release_order_item.item,
            }

            # Add batch_no if specified
            if release_order_item.batch_no:
                stock_filter['batch_no'] = release_order_item.batch_no

            # Add expiry_date if specified
            if release_order_item.expiry_date:
                stock_filter['expiry_date'] = release_order_item.expiry_date

            # Get the stock
            stock = Stock.objects.filter(**stock_filter).first()

            if stock:
                # Find the source rack transaction
                source_transaction = RackTransaction.objects.filter(
                    type=RackTransaction.Type.WRO,
                    warehouse_release_order_item=release_order_item,
                    rackstorage=from_rack_storage,
                    is_reserved=True,
                    is_freezed=False,
                ).first()

                if not source_transaction:
                    form.add_error('from_rack_storage', _("No reserved transaction found for this rack storage"))
                    context = {
                        'record': release_order_item,
                        'form': form,
                        'request': request,
                    }
                    return render(request, 'releases/partials/release_order_item_rack_reassign_form.html', context)

                # Get the original reserved quantity (as a positive number)
                original_quantity = abs(source_transaction.quantity)

                # Check if we're reassigning the full quantity or a partial amount
                if quantity_to_reassign >= original_quantity:
                    # Reassigning the full amount - delete the source transaction
                    source_transaction.delete()

                    # Check if there's an existing transaction for the destination rack
                    existing_transaction = RackTransaction.objects.filter(
                        type=RackTransaction.Type.WRO,
                        warehouse_release_order_item=release_order_item,
                        rackstorage=to_rack_storage,
                        is_reserved=True,
                        is_freezed=False
                    ).first()

                    if existing_transaction:
                        # Update the existing transaction by adding the quantity
                        existing_transaction.quantity -= abs(original_quantity)
                        existing_transaction.save()
                    else:
                        # Create a new transaction for the destination rack
                        RackTransaction.objects.create(
                            type=RackTransaction.Type.WRO,
                            warehouse_release_order_item=release_order_item,
                            rackstorage=to_rack_storage,
                            quantity=-abs(original_quantity),
                            is_reserved=True,
                        )
                else:
                    # Partial reassignment
                    # 1. Update the source transaction to reduce its quantity
                    source_transaction.quantity = -abs(original_quantity - quantity_to_reassign)
                    source_transaction.save()

                    # 2. Check if there's an existing transaction for the destination rack
                    existing_transaction = RackTransaction.objects.filter(
                        type=RackTransaction.Type.WRO,
                        warehouse_release_order_item=release_order_item,
                        rackstorage=to_rack_storage,
                        is_reserved=True,
                        is_freezed=False
                    ).first()

                    if existing_transaction:
                        # Update the existing transaction by adding the quantity
                        existing_transaction.quantity -= abs(quantity_to_reassign)
                        existing_transaction.save()
                    else:
                        # Create a new transaction for the destination rack
                        RackTransaction.objects.create(
                            type=RackTransaction.Type.WRO,
                            warehouse_release_order_item=release_order_item,
                            rackstorage=to_rack_storage,
                            quantity=-abs(quantity_to_reassign),
                            is_reserved=True,
                        )

                    # 3. Create a freezed transaction for the source rack
                    RackTransaction.objects.create(
                        type=RackTransaction.Type.WRO,
                        warehouse_release_order_item=release_order_item,
                        rackstorage=from_rack_storage,
                        quantity=-abs(quantity_to_reassign),
                        is_reserved=True,
                        is_freezed=True,
                    )

                # Update the release order item
                release_order_item.is_rack_assigned = True
                release_order_item.save(update_fields=["is_rack_assigned"])
            else:
                form.add_error(None, _("No matching stock found for this item."))
                context = {
                    'record': release_order_item,
                    'form': form,
                    'request': request,
                }
                return render(request, 'releases/partials/release_order_item_rack_reassign_form.html', context)
        else:
            context = {
                'record': release_order_item,
                'form': form,
                'request': request,
            }
            return render(request, 'releases/partials/release_order_item_rack_reassign_form.html', context)

        # For HTMX requests, return updated HTML fragments
        if request.headers.get('HX-Request'):
            # Prepare context for templates
            context = {'record': release_order_item, 'request': request}
            actions_id = f"release-order-item-actions-{release_order_item.pk}"
            rack_transaction_id = f"release-order-item-rack-transaction-{release_order_item.pk}"

            actions_html = render_to_string('releases/partials/release_order_item_actions.html', context,
                                            request=request)
            rack_transaction_html = render_to_string('releases/partials/release_order_item_rack_transaction.html',
                                                    context, request=request)

            # Return JSON response with all HTML fragments
            response_html = f"""
            <div id="{actions_id}" hx-swap-oob="true">{actions_html}</div>
            <div id="{rack_transaction_id}" hx-swap-oob="true">{rack_transaction_html}</div>
            <div></div>
            """

            trigger_data = {
                "closeModalEvent": None,
                "showNotificationEvent": {
                    "message": "Rack reassigned successfully!",
                    "type": "success"
                }
            }

            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }

            return HttpResponse(response_html, status=200, headers=headers)
        else:
            messages.success(request, _("Rack reassigned successfully!"))
            return redirect(reverse('releases:orders:detail', kwargs={'pk': release_order_item.release_order.pk}))
    except Exception as e:
        messages.error(request, _("Failed to reassign rack: %(error)s") % {'error': str(e)})
        # Return an error response for HTMX
        return HttpResponse(status=500, content=f"Error reassigning rack: {str(e)}")
