from decimal import Decimal

from django.conf import settings

# from django.core.exceptions import ValidationError
from django.db import models
from django.db.models import Sum
from django.urls import reverse
from django.utils.functional import cached_property
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _

from wms.cores.models import (
    DISPLAY_EMPTY_VALUE,
    AbstractBaseModel,
    AbstractSystemNumberModel,
    AbstractSortableModel,
    greater_than_zero,
)
from wms.cores.utils import localtime_now

from wms.apps.inventories.models import Stock, Transaction
from wms.apps.settings.utils import uom_converter


class Transfer(AbstractSystemNumberModel, AbstractBaseModel):
    """Transfer model for Warehouse Management System.

    Available fields:

    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * system_number         (AbstractSystemNumberModel)
    * issued_by
    * transfer_datetime
    * transfer_from
    * transfer_to
    * status
    * remark
    * customer_reference

    """

    class Status(models.TextChoices):
        DRAFT = "Draft", _("Draft")
        PROCESSING = "Processing", _("Processing")
        COMPLETED = "Completed", _("Completed")
        COMPLETED_WITH_REJECT = "Completed with reject", _("Completed with reject")

    issued_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.PROTECT)
    transfer_datetime = models.DateTimeField(verbose_name=_("Transfer Date Time"), default=localtime_now)
    transfer_from = models.ForeignKey(
        "settings.Warehouse",
        related_name="transfer_from",
        limit_choices_to={"is_storage": True},
        on_delete=models.PROTECT,
    )
    transfer_to = models.ForeignKey(
        "settings.Warehouse",
        related_name="transfer_to",
        limit_choices_to={"is_storage": True},
        on_delete=models.PROTECT,
    )
    status = models.CharField(verbose_name=_("Status"), max_length=32, choices=Status.choices, default=Status.DRAFT)
    remark = models.TextField(verbose_name=_("Remark"), max_length=512, blank=True)
    customer_reference = models.CharField(verbose_name=_("Customer Reference"), max_length=64, blank=True)

    class Meta(AbstractBaseModel.Meta):
        ordering = ["-system_number"]

    def __str__(self):
        return f"{self.system_number} :: From {self.transfer_from} :: To {self.transfer_to}"

    def get_absolute_url(self):
        return reverse("transfers:notes:detail", kwargs={"pk": self.pk})

    @cached_property
    def html_status_display(self):
        """Return nice HTML display for status."""
        html_class = ""

        if self.status == Transfer.Status.DRAFT:
            html_class = "badge bg-theme-status-warning"
        elif self.status == Transfer.Status.PROCESSING:
            html_class = "badge bg-theme-status-info"
        elif self.status == Transfer.Status.COMPLETED:
            html_class = "badge bg-theme-status-success"
        elif self.status == Transfer.Status.COMPLETED_WITH_REJECT:
            html_class = "badge bg-theme-status-error"

        return format_html(f'<span class="{html_class}">{self.get_status_display()}</span>') or DISPLAY_EMPTY_VALUE

    def save(self, *args, **kwargs):
        """
        Override save method to ensure transfer_datetime has seconds and milliseconds.
        If seconds and milliseconds are none or 0, assign current datetime's seconds and milliseconds.
        """
        if self.transfer_datetime:
            # Check if seconds and microseconds are 0
            if self.transfer_datetime.second == 0 and self.transfer_datetime.microsecond == 0:
                # Get current datetime
                current_datetime = localtime_now()
                # Update the transfer_datetime with current seconds and microseconds
                self.transfer_datetime = self.transfer_datetime.replace(
                    second=current_datetime.second,
                    microsecond=current_datetime.microsecond
                )

        super().save(*args, **kwargs)


class TransferItem(AbstractBaseModel, AbstractSortableModel):
    """TransferItem model for Warehouse Management System.

    Available fields:

    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * sort_order        (AbstractSortableModel)
    * transfer
    * stock
    * uom
    * quantity
    * remark
    * status

    """

    class Status(models.TextChoices):
        DRAFT = "Draft", _("Draft")
        APPROVED = "Approved", _("Approved")
        REJECTED = "Rejected", _("Rejected")

    transfer = models.ForeignKey("transfers.Transfer", on_delete=models.CASCADE)
    stock = models.ForeignKey("inventories.Stock", on_delete=models.CASCADE)
    uom = models.ForeignKey(
        "settings.UnitOfMeasure",
        verbose_name=_("UOM"),
        on_delete=models.RESTRICT,
        help_text=_("The item will be measured in terms of this unit (e.g.: kg, pcs, box)."),
    )
    quantity = models.DecimalField(
        verbose_name=_("Quantity"),
        max_digits=19,
        decimal_places=6,
        default=Decimal("0"),
        validators=[greater_than_zero],
    )
    remark = models.TextField(verbose_name=_("Remark"), max_length=512, blank=True)
    status = models.CharField(verbose_name=_("Status"), max_length=32, choices=Status.choices, default=Status.DRAFT)

    # is_cleaned = False

    class Meta(AbstractBaseModel.Meta):
        ordering = ["sort_order"]

    def __str__(self):
        return f"{self.transfer.system_number}: {self.sort_order}"

    @cached_property
    def get_position(self):
        """Return position of item in Adjustment."""
        position = (
            list(
                self.__class__.objects.filter(transfer=self.transfer)
                .order_by("sort_order")
                .values_list("pk", flat=True)
            ).index(self.pk)
            + 1
        )

        return position

    @cached_property
    def get_expected_quantity(self):
        """Return current item's expected quantity"""

        expected_quantity = round(self.quantity, self.uom.unit_precision)
        return expected_quantity

    @cached_property
    def get_expected_converted_accurate_quantity(self):
        """Return current item's converted accurate expected quantity"""

        converted_quantity = uom_converter(
            origin_uom=self.uom,
            target_uom=self.stock.item.uom,
            quantity=self.quantity,
        )

        return converted_quantity

    @cached_property
    def get_transaction_approved_quantity(self):
        """Return current item's approved quantity based on transaction's system quantity."""

        transaction_system_quantity = self.transferstockinout_set.all().aggregate(
            Sum("transaction_in__system_quantity")
        ).get("transaction_in__system_quantity__sum", Decimal("0")) or Decimal("0")

        transaction_system_quantity = round(transaction_system_quantity, self.stock.item.uom.unit_precision)
        return transaction_system_quantity

    @cached_property
    def html_status_display(self):
        """Return nice HTML display for status."""
        html_class = ""

        if self.status == TransferItem.Status.DRAFT:
            html_class = "no-export badge bg-theme-status-warning"
        elif self.status == TransferItem.Status.APPROVED:
            html_class = "no-export badge bg-theme-status-success"
        elif self.status == TransferItem.Status.REJECTED:
            html_class = "no-export badge bg-theme-status-error"

        return format_html(f'<span class="{html_class}">{self.get_status_display()}</span>') or DISPLAY_EMPTY_VALUE

    # def clean(self):
    #     # check if release order's release from's choices is in stock's warehouse.
    #     self.is_cleaned = True
    #     if self.stock.warehouse != self.transfer.transfer_from:
    #         raise ValidationError(_(f"Stock not in warehouse. ({self.stock.warehouse})"))
    #     super().clean()

    def save(self, *args, **kwargs):
        # if not self.is_cleaned:
        #     self.full_clean()
        super().save(*args, **kwargs)

        if self.status == TransferItem.Status.APPROVED:

            all_transferitem = self.transfer.transferitem_set.all()
            all_draft_transferitem = all_transferitem.filter(status=TransferItem.Status.DRAFT)
            all_reject_transferitem = all_transferitem.filter(status=TransferItem.Status.REJECTED)

            # if atleast one draft transferitem exist, Transfer status will be PROCESSING
            if all_draft_transferitem.count() > 0:
                self.transfer.status = Transfer.Status.PROCESSING
            # if there are no more draft and atleast one reject exist, COMPLETED_WITH_REJECT
            elif all_reject_transferitem.count() > 0:
                self.transfer.status = Transfer.Status.COMPLETED_WITH_REJECT
            else:
                self.transfer.status = Transfer.Status.COMPLETED
            self.transfer.save(update_fields=["status"])

        elif self.status == TransferItem.Status.REJECTED:
            all_transferitem = self.transfer.transferitem_set.all()
            all_draft_transferitem = all_transferitem.filter(status=TransferItem.Status.DRAFT)

            # if atleast one draft transferitem exist, Transfer status will be PROCESSING
            if all_draft_transferitem.count() > 0:
                self.transfer.status = Transfer.Status.PROCESSING
            # if there are no more draft and atleast one reject exist, COMPLETED_WITH_REJECT
            # in this condition, impossible to have COMPLETED anymore, becoz it's in REJECTED condition
            else:
                self.transfer.status = Transfer.Status.COMPLETED_WITH_REJECT
            self.transfer.save(update_fields=["status"])


class TransferStockInOut(AbstractBaseModel):
    """TransferStockInOut model for Warehouse Management System.

    Available fields:

    * created           (AbstractBaseModel => TimeStampedModel)
    * modified          (AbstractBaseModel => TimeStampedModel)
    * created_by        (AbstractBaseModel)
    * modified_by       (AbstractBaseModel)
    * transfer_item
    * approved_by
    * transfer_datetime
    * uom
    * remark
    * transaction_in
    * transaction_out

    """

    transfer_item = models.ForeignKey(
        "transfers.TransferItem",
        on_delete=models.CASCADE,
        help_text=_("Each Transfer Item indicate a relation to item's released quantity."),
    )
    approved_by = models.ForeignKey(settings.AUTH_USER_MODEL, related_name="+", on_delete=models.RESTRICT)
    transfer_datetime = models.DateTimeField(verbose_name=_("Stock Out Date Time"))
    uom = models.ForeignKey("settings.UnitOfMeasure", verbose_name=_("UOM"), on_delete=models.RESTRICT)
    remark = models.TextField(verbose_name=_("Remark"), max_length=512, blank=True)
    transaction_in = models.ForeignKey(
        "inventories.Transaction", related_name="transactions_in", on_delete=models.SET_NULL, null=True, blank=True
    )
    transaction_out = models.ForeignKey(
        "inventories.Transaction", related_name="transactions_out", on_delete=models.SET_NULL, null=True, blank=True
    )

    class Meta(AbstractBaseModel.Meta):
        ordering = ["transfer_datetime", "pk"]

    def __str__(self):
        return (
            f"From {self.transfer_item.stock} "
            f"To {self.transfer_item.transfer.transfer_to} "
            f":: {self.transfer_item.quantity}"
        )

    def save(self, *args, **kwargs):

        if self.transaction_in is None and self.approved_by:
            # to check if destination's warehouse have the stock object
            # else then create a new one
            stock_in_obj, created = Stock.objects.get_or_create(
                warehouse=self.transfer_item.transfer.transfer_to,
                item=self.transfer_item.stock.item,
                batch_no=self.transfer_item.stock.batch_no,
                expiry_date=self.transfer_item.stock.expiry_date,
            )

            self.transaction_in = Transaction.objects.create(
                stock=stock_in_obj,
                transaction_datetime=self.transfer_datetime,
                quantity=self.transfer_item.get_expected_converted_accurate_quantity,
                system_number_ref=self.transfer_item.transfer.system_number,
                is_internal_transfer=True,
                uom=self.uom,
                created_by=self.approved_by,
            )
        if self.transaction_out is None and self.approved_by:
            self.transaction_out = Transaction.objects.create(
                stock=self.transfer_item.stock,
                transaction_datetime=self.transfer_datetime,
                quantity=-abs(self.transfer_item.get_expected_converted_accurate_quantity),
                system_number_ref=self.transfer_item.transfer.system_number,
                is_internal_transfer=True,
                uom=self.uom,
                created_by=self.approved_by,
            )

        super().save(*args, **kwargs)

        # this will trigger becoz it must be approve already,
        # else if rejected, frontend will redirect to another path
        if self.transfer_item.status != TransferItem.Status.APPROVED:
            self.transfer_item.status = TransferItem.Status.APPROVED
            self.transfer_item.save(update_fields=["status"])
