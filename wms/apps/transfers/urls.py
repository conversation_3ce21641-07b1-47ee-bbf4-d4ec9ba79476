from django.urls import include, path

from wms.apps.transfers.views.transfer import (
    TransferListView,
    TransferCreateView,
    TransferUpdateView,
    TransferDetailHomeView,
    TransferDetailView,
    TransferDataTableDetailView,
    TransferItemListView,
    transfer_delete_form,
    transfer_delete,
)
from wms.apps.transfers.views.transfer_item import (
    transfer_item_approve_form, transfer_item_reject_form,
    transfer_item_approve, transfer_item_reject
)

# from wss.apps.transfers.views import (
#     transfer_create_view,
#     transfer_datatables_view,
#     transfer_delete_view,
#     transfer_detail_datatables_view,
#     transfer_detail_view,
#     transfer_history_list_datatables_view,
#     transfer_history_list_view,
#     transfer_history_modified_view,
#     transfer_info_detail_view,
#     transfer_info_update_view,
#     transfer_item_approve_view,
#     transfer_item_delete_view,
#     transfer_item_reject_view,
#     transfer_item_stockinout_view,
#     transfer_item_tr_view,
#     transfer_items_list_view,
#     transfer_list_view,
#     transfer_rejects_list_view,
#     transfer_status_detail_view,
#     transfer_update_view,
# )

app_name = "transfers"

transfers_urlpatterns = [
    path("", view=TransferListView.as_view(), name="list"),
    path("create/", view=TransferCreateView.as_view(), name="create"),
    path("<int:pk>/update/", view=TransferUpdateView.as_view(), name="update"),
    path("<int:pk>", view=TransferDataTableDetailView.as_view(), name="panel"),
    path("<int:pk>/detail/", view=TransferDetailView.as_view(), name="detail"),
    path("<int:pk>/detail-home/", view=TransferDetailHomeView.as_view(), name="detail_home"),
    path("<int:pk>/item/list/", view=TransferItemListView.as_view(), name="item_list"),
    path("item/<int:pk>/approve-form/", view=transfer_item_approve_form, name="item_approve_form"),
    path("item/<int:pk>/reject-form/", view=transfer_item_reject_form, name="item_reject_form"),
    path("item/<int:pk>/approve/", view=transfer_item_approve, name="item_approve"),
    path("item/<int:pk>/reject/", view=transfer_item_reject, name="item_reject"),
    path("<int:pk>/delete-form/", view=transfer_delete_form, name="delete_form"),
    path("<int:pk>/delete/", view=transfer_delete, name="delete"),
    # path("delete/<int:pk>/", view=transfer_delete_view, name="delete"),
    # path("detail/<int:pk>/info/", view=transfer_info_detail_view, name="info"),
    # path("detail/<int:pk>/info/update/", view=transfer_info_update_view, name="info_update"),
    # path("detail/<int:pk>/status/", view=transfer_status_detail_view, name="status"),
    # path("detail/<int:pk>/items/", view=transfer_items_list_view, name="items"),
    # path("detail/<int:pk>/rejects/", view=transfer_rejects_list_view, name="rejects"),
    # path("datatables/", view=transfer_datatables_view, name="datatables"),
    # path("datatables-detail/", view=transfer_detail_datatables_view, name="datatables-detail"),
    # path("detail/<int:pk>/history/", view=transfer_history_list_view, name="history"),
    # path("detail/<int:pk>/history/popup-modified", view=transfer_history_modified_view, name="history-modified"),
    # path("detail/<int:pk>/datatables-history/", view=transfer_history_list_datatables_view, name="datatables-history"),
]

# transfers_item_urlpatterns = [
#     path("delete/<int:pk>/", view=transfer_item_delete_view, name="delete"),
#     path("detail/<int:pk>/approve/", view=transfer_item_approve_view, name="approve"),
#     path("detail/<int:pk>/reject/", view=transfer_item_reject_view, name="reject"),
#     path("detail/<int:pk>/tr/", view=transfer_item_tr_view, name="tr"),
#     path("detail/<int:pk>/stockinout/", view=transfer_item_stockinout_view, name="stockinout"),
# ]

urlpatterns = [
    path(
        "notes/",
        include((transfers_urlpatterns, "transfers.note"), namespace="notes"),
    ),
    # path(
    #     "transter-note-item/",
    #     include(
    #         (transfers_item_urlpatterns, "transfers.transfer_note_item"),
    #         namespace="transfer_notes_item",
    #     ),
    # ),
]
