import json

from crispy_forms.helper import FormHelper
from django.contrib import messages
from django.db import transaction
from django.db.models import QuerySet
from django.forms import inlineformset_factory, ValidationError
from django.shortcuts import redirect
from django.urls import reverse_lazy, reverse
from django.shortcuts import get_object_or_404, render
from django.http import HttpResponse
from django.views.decorators.http import require_POST

from wms.apps.transfers.filters import TransferFilter, TransferDataTableFilter
from wms.apps.transfers.tables import TransferTable, TransferDetailTable, TransferItemTable
from wms.apps.transfers.forms import TransferForm, TransferItemForm
from wms.apps.transfers.models import Transfer, TransferItem
from wms.cores.mixins import ExportTableMixin
from wms.cores.views import CoreSingleTableView, CoreCreateView, CoreUpdateView, CoreDetailView, CoreDataTableDetailView


class TransferListView(ExportTableMixin, CoreSingleTableView):
    """
    View for displaying and managing the list of Items.
    Supports:
    - Pagination
    - Column visibility
    - Searching
    - Export functionality
    - HTMX partial updates
    """
    model = Transfer
    table_class = TransferTable
    template_name = "cores/list_table.html"
    partial_template_name = "cores/table_partial.html"
    queryset = Transfer.objects.all()
    filterset_class = TransferFilter

    # Search configuration
    search_fields = ["system_number"]

    # Export configuration
    export_name = "transfers"
    export_permission = []  # Empty list means no specific permissions required


class TransferFormsetMixin:
    """Mixin to handle TransferItem inline formset with Crispy Forms (Table Layout)."""
    formset = None

    def get_formset(self):
        """Creates and returns the TransferItem formset with a Crispy Helper for table layout."""
        transfer_item_form_set = inlineformset_factory(
            Transfer,
            TransferItem,
            form=TransferItemForm,
            extra=1,
            validate_min=True,
            can_delete=True
        )

        # Create the helper for the formset
        helper = FormHelper()
        helper.form_tag = False
        helper.disable_csrf = True
        helper.template = 'tailwind/table_inline_formset.html'
        helper.formset_header_title = "Items"

        if self.request.method == 'POST':
            formset_instance = transfer_item_form_set(
                self.request.POST,
                self.request.FILES,
                instance=self.object,
                prefix='items'
            )
        else:
            # For update view, set extra=0 if there are existing items
            initial_extra = 0 if (self.object and self.object.transferitem_set.exists()) else 1
            transfer_item_form_set.extra = initial_extra

            formset_instance = transfer_item_form_set(
                instance=self.object,
                prefix='items'
            )

        # Set the request on each form in the formset
        for form in formset_instance:
            form.request = self.request

        formset_instance.helper = helper
        return formset_instance

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.formset is None:
            self.formset = self.get_formset()

        # Ensure management form data is correct
        if self.object and not self.request.POST:  # If this is an update view
            total_forms = self.object.transferitem_set.count()
            self.formset.management_form.initial['TOTAL_FORMS'] = total_forms
            self.formset.management_form.initial['INITIAL_FORMS'] = total_forms

        context['formset'] = self.formset
        return context

    # form_valid and form_invalid remain the same
    def form_valid(self, form):
        context = self.get_context_data()
        formset = context['formset']  # Get formset with helper

        if formset.is_valid():
            # Save the main Transfer object
            transfer_object = form.save()
            # Store the Transfer object in self.object
            self.object = transfer_object
            formset.instance = transfer_object

            # To handle delete transfer items
            transfer_items = transfer_object.transferitem_set.all()
            updated_items = []

            for each_formset in formset:
                updated_items.append(each_formset.cleaned_data.get("stock_id"))

            for item in transfer_items:
                if item not in updated_items:
                    item.delete()

            # Set request on each form in the formset to ensure created_by and modified_by are set
            for transfer in formset:
                if hasattr(transfer, 'instance') and transfer.instance:
                    # Only set request if the form has an instance
                    transfer.request = self.request

            # Save the formset but store the result in a separate variable
            formset.save()

            return super().form_valid(form)
        else:
            return super().form_invalid(form)

    def form_invalid(self, form):
        """Handle invalid main form or formset."""
        # Ensure the invalid formset (with helper) is put back into the context
        if self.formset is None or not hasattr(self.formset, 'helper'):
            self.formset = self.get_formset()  # Recreate formset with helper
        elif not hasattr(self.formset, 'helper'):
            self.formset.helper = self.get_formset().helper  # Re-attach helper

        return super().form_invalid(form)


# --- Existing Mixin (from context) ---
class TransferCreateAndUpdateMixin:
    """Mixin for Transfer create and update."""

    model = Transfer
    form_class = TransferForm
    template_name = "transfers/transfer_form.html"  # Ensure this template renders the formset
    # Define success_url here or in specific views
    # success_url = reverse_lazy("transfers:transfer_list")
    cancel_url = reverse_lazy("transfers:notes:list")

    def get_initial(self):
        initial = super().get_initial()
        # Only set issued_by for new objects (CreateView)
        if self.object is None:
            initial["issued_by"] = self.request.user
        return initial


class TransferCreateView(TransferFormsetMixin, TransferCreateAndUpdateMixin, CoreCreateView):
    """View to create a new Transfer with its items."""
    # Attributes from CoreCreateView/TransferCreateAndUpdateMixin might be needed
    section_title = "Create Transfer"
    section_desc = ""
    submit_text = "Save"
    success_url = "transfers:notes:panel"
    cancel_url = "transfers:notes:list"


class TransferUpdateView(TransferFormsetMixin, TransferCreateAndUpdateMixin, CoreUpdateView):
    """View to update an existing Transfer and its items."""
    section_title = "Update Transfer"
    section_desc = ""
    submit_text = "Update"
    success_url = "transfers:notes:panel"

    def get(self, request, *args, **kwargs):
        self.object = self.get_object()
        if self.object.status != Transfer.Status.DRAFT:
            messages.error(self.request, "Only Draft Transfer can be edited")
            return redirect(reverse("transfers:notes:list"))
        return super().get(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        self.formset.extra = 0

        return context


class TransferDetailHomeView(CoreDetailView):
    """
    View for displaying the main detail page of a Transfer.
    This serves as the home view for the transfer detail panel.
    """
    model = Transfer
    template_name = 'transfers/mains/home.html'
    context_object_name = "transfer"


class TransferDetailView(CoreDetailView):
    """
    View for displaying the detailed information of a Transfer.
    This is used as a partial view within the detail panel.
    """
    model = Transfer
    template_name = 'transfers/partials/detail.html'
    context_object_name = "transfer"


class TransferDataTableDetailView(CoreDataTableDetailView):
    """
    View that combines a detail view with a data table.
    Provides a split panel layout with table and detail view.
    """
    model = Transfer
    table_class = TransferDetailTable
    context_object_name = "transfer"
    partial_view = TransferDetailHomeView
    search_fields = ["system_number"]
    filterset_class = TransferDataTableFilter  # Uncomment when filter class is created


class TransferItemListView(ExportTableMixin, CoreSingleTableView):
    """
    View for displaying and managing the list of Items.
    Supports:
    - Pagination
    - Column visibility
    - Searching
    - Export functionality
    - HTMX partial updates
    """
    model = TransferItem
    table_class = TransferItemTable
    template_name = "cores/datatable_detail_view_table.html"
    partial_template_name = "cores/table_partial.html"

    # Search configuration
    search_fields = ["stock__item__code", "stock__batch_no", "stock__item__name"]

    # Export configuration
    export_name = "transfer_items"
    export_permission = []  # Empty list means no specific permissions required

    # HTMX configuration, override default to prevent htmx target conflict
    # Map this with own partial element id, use this when under single page there is more than 1 htmx table
    htmx_target = 'table-content-partial'

    def get_queryset(self) -> QuerySet:
        queryset = super().get_queryset()
        # Apply additional filter for this specific view
        return queryset.filter(transfer_id=self.kwargs.get('pk'))

    def get_htmx_base_url(self):
        """
        Override to provide the dynamic URL based on the current item's PK.
        This URL will be used for all HTMX requests and querystring operations.
        """
        pk = self.kwargs.get('pk')
        if pk:
            return reverse("transfers:notes:item_list", kwargs={"pk": pk})
        return None



###########################################################################
# transfer delete
###########################################################################


def transfer_delete_form(request, pk):
    """
    Display the form for deleting a transfer.
    """
    transfer = get_object_or_404(Transfer, pk=pk)

    # Check if the transfer is in a status that allows deletion
    allowed_statuses = [
        Transfer.Status.DRAFT,
    ]

    if transfer.status not in allowed_statuses:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Only Transfer in 'New' status can be deleted.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request,
                       _("Only Transfer in 'New' status can be deleted."))
        return HttpResponse(status=400, content="Transfer not eligible for deletion.")

    context = {
        'transfer': transfer,
        'request': request,
    }

    return render(request, 'transfers/partials/transfer_delete_form.html', context)


@require_POST
def transfer_delete(request, pk):
    """
    Delete a transfer.
    """
    transfer = get_object_or_404(Transfer, pk=pk)

    # Check if the transfer is in a status that allows deletion
    allowed_statuses = [
        Transfer.Status.DRAFT,
    ]

    if transfer.status not in allowed_statuses:
        if request.headers.get('HX-Request'):
            headers = {
                'HX-Trigger': json.dumps({
                    "closeModalEvent": None,
                    "showNotificationEvent": {
                        "message": "Only Transfer in 'New' status can be deleted.",
                        "type": "error"
                    }
                })
            }
            return HttpResponse(status=400, headers=headers)
        messages.error(request,
                       _("Only Transfer in 'New' status can be deleted."))
        return HttpResponse(status=400, content="Transfer not eligible for deletion.")

    try:
        # Attempt to delete the transfer
        transfer.delete()

        # For HTMX requests, return success response
        if request.headers.get('HX-Request'):
            trigger_data = {
                "closeModalEvent": None,
                "showNotificationEvent": {
                    "message": "Transfer deleted successfully!",
                    "type": "success"
                },
                # Redirect to the list page after deletion
                "redirectEvent": {"url": reverse("transfers:notes:list")}
            }

            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            return HttpResponse(status=200, headers=headers)

        # For non-HTMX requests, redirect to the list page
        messages.success(request, _("Transfer deleted successfully."))
        return redirect(reverse("transfers:notes:list"))

    except Exception as e:
        # Handle validation errors or other exceptions
        error_message = str(e)

        # For HTMX requests, return error response
        if request.headers.get('HX-Request'):
            trigger_data = {
                "closeModalEvent": None,
                "showNotificationEvent": {
                    "message": f"Failed to delete transfer: {error_message}",
                    "type": "error"
                }
            }

            headers = {
                'HX-Trigger': json.dumps(trigger_data)
            }
            return HttpResponse(status=400, headers=headers)

        # For non-HTMX requests, redirect back with error message
        messages.error(request, _("Failed to delete transfer: %(error)s") % {'error': error_message})
        return redirect(reverse("transfers:notes:panel", kwargs={"pk": pk}))


###########################################################################
# end transfer delete
###########################################################################
