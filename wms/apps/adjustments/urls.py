from django.urls import include, path

from wms.apps.adjustments.views import (
    AdjustmentListView,
    AdjustmentCreateView,
    AdjustmentUpdateView,
    AdjustmentDetailHomeView,
    AdjustmentDetailView,
    AdjustmentDataTableDetailView,
    AdjustmentItemListView,
    adjustment_delete_form,
    adjustment_delete,
    adjustment_item_approve_form,
    adjustment_item_reject_form,
    adjustment_item_approve,
    adjustment_item_reject,
#     adjustment_create_view,
#     adjustment_datatables_view,
#     adjustment_delete_view,
#     adjustment_detail_datatables_view,
#     adjustment_detail_view,
#     adjustment_history_list_datatables_view,
#     adjustment_history_list_view,
#     adjustment_history_modified_view,
#     adjustment_info_detail_view,
#     adjustment_info_update_view,
#     adjustment_item_approve_view,
#     adjustment_item_create_view,
#     adjustment_item_delete_view,
#     adjustment_item_reject_view,
#     adjustment_item_stockins_view,
#     adjustment_item_tr_view,
#     adjustment_items_list_view,
#     adjustment_list_view,
#     adjustment_rejects_list_view,
#     adjustment_status_detail_view,
#     adjustment_update_view,
)

app_name = "adjustments"


adjustments_urlpatterns = [
    path("", view=AdjustmentListView.as_view(), name="list"),
    path("create/", view=AdjustmentCreateView.as_view(), name="create"),
    path("<int:pk>/update/", view=AdjustmentUpdateView.as_view(), name="update"),
    path("<int:pk>", view=AdjustmentDataTableDetailView.as_view(), name="panel"),
    path("<int:pk>/detail/", view=AdjustmentDetailView.as_view(), name="detail"),
    path("<int:pk>/detail-home/", view=AdjustmentDetailHomeView.as_view(), name="detail_home"),
    path("<int:pk>/item/list/", view=AdjustmentItemListView.as_view(), name="item_list"),
    path("item/<int:pk>/approve-form/", view=adjustment_item_approve_form, name="item_approve_form"),
    path("item/<int:pk>/reject-form/", view=adjustment_item_reject_form, name="item_reject_form"),
    path("item/<int:pk>/approve/", view=adjustment_item_approve, name="item_approve"),
    path("item/<int:pk>/reject/", view=adjustment_item_reject, name="item_reject"),
    path("<int:pk>/delete-form/", view=adjustment_delete_form, name="delete_form"),
    path("<int:pk>/delete/", view=adjustment_delete, name="delete"),
#     path("", view=adjustment_list_view, name="list"),
#     path("create/", view=adjustment_create_view, name="create"),
#     path("delete/<int:pk>/", view=adjustment_delete_view, name="delete"),
#     path("detail/<int:pk>/", view=adjustment_detail_view, name="detail"),
#     path("detail/<int:pk>/update/", view=adjustment_update_view, name="update"),
#     path("detail/<int:pk>/info/", view=adjustment_info_detail_view, name="info"),
#     path("detail/<int:pk>/info/update/", view=adjustment_info_update_view, name="info_update"),
#     path("detail/<int:pk>/status/", view=adjustment_status_detail_view, name="status"),
#     path("detail/<int:pk>/items/", view=adjustment_items_list_view, name="items"),
#     path("detail/<int:pk>/rejects/", view=adjustment_rejects_list_view, name="rejects"),
#     path("datatables/", view=adjustment_datatables_view, name="datatables"),
#     path("datatables-detail/", view=adjustment_detail_datatables_view, name="datatables-detail"),
#     path("detail/<int:pk>/history/", view=adjustment_history_list_view, name="history"),
#     path("detail/<int:pk>/history/popup-modified", view=adjustment_history_modified_view, name="history-modified"),
#     path(
#         "detail/<int:pk>/datatables-history/", view=adjustment_history_list_datatables_view, name="datatables-history"
#     ),
]

# adjustments_item_urlpatterns = [
#     path("<int:adj_pk>/create/", view=adjustment_item_create_view, name="create"),
#     path("delete/<int:pk>/", view=adjustment_item_delete_view, name="delete"),
#     path("detail/<int:pk>/stockin/", view=adjustment_item_stockins_view, name="stockins"),
#     path("detail/<int:pk>/approve/", view=adjustment_item_approve_view, name="approve"),
#     path("detail/<int:pk>/reject/", view=adjustment_item_reject_view, name="reject"),
#     path("detail/<int:pk>/tr/", view=adjustment_item_tr_view, name="tr"),
# ]

urlpatterns = [
    path(
        "adjustment/",
        include((adjustments_urlpatterns, "adjustments.adjustment"), namespace="adjustment"),
    ),
#     path(
#         "adjustment-item/",
#         include(
#             (adjustments_item_urlpatterns, "adjustments.adjustment_item"),
#             namespace="adjustments_item",
#         ),
#     ),
]
