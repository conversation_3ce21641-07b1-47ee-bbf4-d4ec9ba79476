from decimal import Decimal

from django.db import models
from django.db.models import Sum, Q, F, Value, <PERSON><PERSON><PERSON><PERSON>, OuterRef, Subquery
from django.db.models.functions import Coalesce, Concat


from wms.cores.utils import normalize_decimal

# from wss.apps.settings.utils import uom_converter


class RackTransactionManager(models.Manager):
    """Manager for RackTransactionManager model.

    In consideration of aspects:
    - warehouse
    - item
    - batch_no
    - expiry_date
    - rack
        - available quantity
        - reserved quantity
    """

    def balance_by_item(
        self,
        rack=None,
        rack_descendants_consideration=False,
        balance_type="available",
        item=None,
        is_freezed=False,
    ):
        """Return balance of given item.

        Args:
            - rack:
                - Rack object
            - rack_descendants_consideration:
                - Boolean
            - balance_type -> str:
                - "physical" | physical = current total system transaction
                - "available" | available = current total system transaction - reserved
                - "reserved"  | reserved = ONLY reserved
            - item
                - Item Object
        """

        ###########################################################################
        # *NOTE: commenting this fragment out first due to needed to use Subquery
        #        but this will improve performance based on indexing.
        #        may revisit this method IF the performance is really bad.
        # from django.db.models import OuterRef, Subquery, Exists
        # rackstorage_qs = RackStorage.objects.filter(
        #     stock__item=item,
        #     pk=OuterRef('rackstorage_id')
        # )
        # balance = RackTransaction.objects.annotate(
        #     has_item=Exists(rackstorage_qs)
        # ).filter(
        #     has_item=True,
        #     is_reserved=False,
        # ).aggregate(Sum("quantity"))["quantity__sum"] or Decimal("0")
        ###########################################################################

        balance = 0

        if balance_type == "available":
            is_reserved = None
        elif balance_type == "reserved":
            is_reserved = True
        else:
            # to query expected physical stock
            is_reserved = False

        if rack:
            if rack_descendants_consideration is True:

                # *NOTE*: if this query is slow, considering casting descendants_and_self_qs
                #         into a "list of ID" then filter id__in instead of rackstorage__rack__in
                descendants_and_self_qs = rack.get_descendants_and_self()

                if is_reserved is None:
                    balance = super().get_queryset().filter(
                        rackstorage__rack__in=descendants_and_self_qs,
                        rackstorage__stock__item=item,
                    ).aggregate(Sum("quantity")).get("quantity__sum", Decimal("0")) or Decimal("0")
                else:
                    balance = super().get_queryset().filter(
                        rackstorage__rack__in=descendants_and_self_qs,
                        rackstorage__stock__item=item,
                        is_reserved=is_reserved,
                        is_freezed=is_freezed,
                    ).aggregate(Sum("quantity")).get("quantity__sum", Decimal("0")) or Decimal("0")
            else:
                balance = super().get_queryset().filter(
                    rackstorage__rack=rack,
                    rackstorage__stock__item=item,
                    is_reserved=is_reserved,
                    is_freezed=is_freezed,
                ).aggregate(Sum("quantity")).get("quantity__sum", Decimal("0")) or Decimal("0")
        else:
            if is_reserved is None:
                balance = super().get_queryset().filter(
                    rackstorage__stock__item=item,
                ).aggregate(Sum("quantity")).get("quantity__sum", Decimal("0")) or Decimal("0")

            else:
                balance = super().get_queryset().filter(
                    rackstorage__stock__item=item,
                    is_reserved=is_reserved,
                    is_freezed=is_freezed,
                ).aggregate(Sum("quantity")).get("quantity__sum", Decimal("0")) or Decimal("0")

        return abs(normalize_decimal(balance))

    def balance_by_item_batch_no(
        self,
        rack=None,
        rack_descendants_consideration=False,
        balance_type="available",
        item=None,
        batch_no=None,
        is_freezed=False,
    ):
        """Return balance of given item and batch_no.

        Args:
            - rack:
                - Rack object
            - rack_descendants_consideration:
                - Boolean
            - balance_type -> str:
                - "physical"  | physical = current total system transaction
                - "available" | available = current total system transaction - reserved
                - "reserved"  | reserved = ONLY reserved
            - item:
                - Item Object
            - batch_no -> str:
                - "BN001"
        """
        balance = 0

        if balance_type == "available":
            is_reserved = None
        elif balance_type == "reserved":
            is_reserved = True
        else:
            # to query expected physical stock
            is_reserved = False

        if rack:
            if rack_descendants_consideration is True:

                # *NOTE*: if this query is slow, considering casting descendants_and_self_qs
                #         into a "list of ID" then filter id__in instead of rackstorage__rack__in
                descendants_and_self_qs = rack.get_descendants_and_self()

                # to query expected available on paper stock balance
                if is_reserved is None:
                    balance = super().get_queryset().filter(
                        rackstorage__rack__in=descendants_and_self_qs,
                        rackstorage__stock__item=item,
                        rackstorage__stock__batch_no=batch_no,
                    ).aggregate(Sum("quantity")).get("quantity__sum", Decimal("0")) or Decimal("0")
                else:
                    balance = super().get_queryset().filter(
                        rackstorage__rack__in=descendants_and_self_qs,
                        rackstorage__stock__item=item,
                        rackstorage__stock__batch_no=batch_no,
                        is_reserved=is_reserved,
                        is_freezed=is_freezed,
                    ).aggregate(Sum("quantity")).get("quantity__sum", Decimal("0")) or Decimal("0")
            else:
                # to query expected available on paper stock balance
                if is_reserved is None:
                    balance = super().get_queryset().filter(
                        rackstorage__rack=rack,
                        rackstorage__stock__item=item,
                        rackstorage__stock__batch_no=batch_no,
                    ).aggregate(Sum("quantity")).get("quantity__sum", Decimal("0")) or Decimal("0")
                else:
                    balance = super().get_queryset().filter(
                        rackstorage__rack=rack,
                        rackstorage__stock__item=item,
                        rackstorage__stock__batch_no=batch_no,
                        is_reserved=is_reserved,
                        is_freezed=is_freezed,
                    ).aggregate(Sum("quantity")).get("quantity__sum", Decimal("0")) or Decimal("0")
        else:
            if is_reserved is None:
                balance = super().get_queryset().filter(
                    rackstorage__stock__item=item,
                    rackstorage__stock__batch_no=batch_no,
                ).aggregate(Sum("quantity")).get("quantity__sum", Decimal("0")) or Decimal("0")
            else:
                balance = super().get_queryset().filter(
                    rackstorage__stock__item=item,
                    rackstorage__stock__batch_no=batch_no,
                    is_reserved=is_reserved,
                    is_freezed=is_freezed,
                ).aggregate(Sum("quantity")).get("quantity__sum", Decimal("0")) or Decimal("0")

        return abs(normalize_decimal(balance))

    def balance_by_stock(
        self,
        rack=None,
        rack_descendants_consideration=False,
        balance_type="available",
        stock=None,
        is_freezed=False,
    ):
        """Return balance of given item and batch_no.

        Args:
            - rack:
                - Rack object
            - rack_descendants_consideration:
                - Boolean
            - balance_type -> str:
                - "physical"  | physical = current total system transaction
                - "available" | available = current total system transaction - reserved
                - "reserved"  | reserved = ONLY reserved
            - is_freezed -> boolean:
                - default as False, if input is True, will return the freezed racktransaction
            - stock:
                - Stock Object
        """
        balance = 0

        if balance_type == "available":
            is_reserved = None
        elif balance_type == "reserved":
            is_reserved = True
        else:
            # to query expected physical stock
            is_reserved = False

        if rack:
            if rack_descendants_consideration is True:

                # *NOTE*: if this query is slow, considering casting descendants_and_self_qs
                #         into a "list of ID" then filter id__in instead of rackstorage__rack__in
                descendants_and_self_qs = rack.get_descendants_and_self()

                # to query expected available on paper stock balance
                if is_reserved is None:
                    balance = super().get_queryset().filter(
                        rackstorage__rack__in=descendants_and_self_qs,
                        rackstorage__stock=stock,
                    ).aggregate(Sum("quantity")).get("quantity__sum", Decimal("0")) or Decimal("0")
                else:
                    balance = super().get_queryset().filter(
                        rackstorage__rack__in=descendants_and_self_qs,
                        rackstorage__stock=stock,
                        is_reserved=is_reserved,
                        is_freezed=is_freezed,
                    ).aggregate(Sum("quantity")).get("quantity__sum", Decimal("0")) or Decimal("0")
            else:
                # to query expected available on paper stock balance
                if is_reserved is None:
                    balance = super().get_queryset().filter(
                        rackstorage__rack=rack,
                        rackstorage__stock=stock,
                    ).aggregate(Sum("quantity")).get("quantity__sum", Decimal("0")) or Decimal("0")
                else:
                    balance = super().get_queryset().filter(
                        rackstorage__rack=rack,
                        rackstorage__stock=stock,
                        is_reserved=is_reserved,
                        is_freezed=is_freezed,
                    ).aggregate(Sum("quantity")).get("quantity__sum", Decimal("0")) or Decimal("0")
        # without rack consideration
        else:
            if is_reserved is None:
                balance = super().get_queryset().filter(
                    rackstorage__stock=stock,
                ).aggregate(Sum("quantity")).get("quantity__sum", Decimal("0")) or Decimal("0")

            else:
                balance = super().get_queryset().filter(
                    rackstorage__stock=stock,
                    is_reserved=is_reserved,
                    is_freezed=is_freezed,
                ).aggregate(Sum("quantity")).get("quantity__sum", Decimal("0")) or Decimal("0")

        return abs(normalize_decimal(balance))
