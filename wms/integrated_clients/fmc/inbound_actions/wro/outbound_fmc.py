import logging
import re
from datetime import datetime, timed<PERSON>ta
from decimal import Decimal

from django.conf import settings
from django.utils.text import get_valid_filename

import xmltodict

from wms.cores.utils import localtime_now

from wms.apps.consignees.models import <PERSON><PERSON><PERSON><PERSON><PERSON>, Consignee, <PERSON><PERSON>ddress
from wms.apps.consignors.models import Consignor
from wms.apps.inventories.models import Item
from wms.apps.releases.models import UPLOAD_PATH, WarehouseReleaseOrder, WarehouseReleaseOrderItem
from wms.apps.settings.models import UnitOfMeasure, Warehouse

logger = logging.getLogger("import_outbound_fmc")


class ImportOutboundFMC:
    """
    Import Outbound class for FMC consignor.

    After self.cleaned(), it will create the following example:

    error_messages_dict = {
        "__all__": ["File PT0622SBH-02.txt uploaded in GRN-22-000001"],
        "3": ["Invalid number of column at line 3"],
        ...
    }
    error_messages_list = [
        "File PT0622SBH-02.txt uploaded in GRN-22-000001",
        "Invalid number of column at line 3",
        ...
    ]

    """

    _valid = False
    consignor = None
    consignee = None
    outbound_delivery_no = None
    error_messages_dict = {}
    error_messages_list = []
    cleaned_batch_split_list = []
    pre_batch_split_checking_dict = {}
    consignor_code = "FMCSB"
    uom_symbol = "PCE"
    uom = None
    release_datetime = None
    warehouses = None
    location = None
    fmc_naming_warehouse = None
    despatch_advice = None
    shipping_address = ""
    shipping_phone = ""
    shipping_mobile = ""
    shipping_attn = ""

    def __init__(self, file):
        self.file = file
        self.consignor = Consignor.objects.get(code=self.consignor_code)
        self.consignee = None
        self.outbound_delivery_no = None
        self.error_messages_dict = {}
        self.error_messages_list = []
        self.cleaned_batch_split_list = []
        self.item_key_by_item_code = {}
        self.pre_batch_split_checking_dict = {}
        self.uom = UnitOfMeasure.objects.get(symbol=self.uom_symbol)
        self.release_datetime = None
        self.warehouses = None
        self.location = None
        self.fmc_naming_warehouse = None
        self.despatch_advice = None
        self.shipping_address = ""
        self.shipping_phone = ""
        self.shipping_mobile = ""
        self.shipping_attn = ""

    def __str__(self):
        return str(self.file) or ""

    def _create_new_consignee(self, address_dict, user, postal_code):
        """
        Expected input format:
        address_dict = {
            'city': "SEPANG",
            'countryCode': "MY",
            'name': "NSCMH DIALYSIS CARE CAWANGAN SEPANG",
            'postalCode': "43900",
            'streetAddressOne': "LOT 1 JALAN SEPANG",
            'streetAddressTwo': "PEKAN SEPANG",
        }

        odd case:
            20240601: name = ABC (M) SDN BHD
            20240904: name = PWRM ( BM ) DIALYSIS CENTRE
        """
        original_name = address_dict["name"].strip()
        one_or_two_blank_space_pattern = r"\s{1,2}"

        original_name_breakdown_list = re.split(one_or_two_blank_space_pattern, original_name)

        initial = ""
        avoid_symbol_list = ["(", ")"]
        for split in original_name_breakdown_list:
            temp = ""
            if split[0] in avoid_symbol_list and len(split) == 1:
                pass
            elif split[0] == "(":
                temp = split[1]
            else:
                temp = split[0]
            initial += temp

        temp_initial = f"FMCSB-{initial.upper()}" + "EDI_TEMPORARY"

        self.consignee = Consignee.objects.create(
            created_by=user,
            company_name=original_name,
            display_name=original_name,
            code=temp_initial,
            consignor=self.consignor,
        )

        new_code = self.consignee.code.replace("EDI_TEMPORARY", f"-{self.consignee.pk:06d}")

        self.consignee.code = new_code
        self.consignee.save()

        country = ""
        if "countryCode" in address_dict and address_dict["countryCode"] in ["MY", "EMY"]:
            country = "MALAYSIA"

        city = ""
        if "city" in address_dict:
            city = address_dict["city"]

        address_street_1 = ""
        if "streetAddressOne" in address_dict:
            address_street_1 = address_dict["streetAddressOne"]

        address_street_2 = ""
        if "streetAddressTwo" in address_dict:
            address_street_2 = address_dict["streetAddressTwo"]

        address_street_3 = ""
        if "streetAddressThree" in address_dict:
            address_street_3 = address_dict["streetAddressThree"]
            address_street_2 += f" {address_street_3}"

        address_postal_code = ""
        if "postalCode" in address_dict:
            address_postal_code = postal_code

        ShippingAddress.objects.create(
            created_by=user,
            address_street_1=address_street_1,
            address_street_2=address_street_2,
            address_postal_code=address_postal_code,
            address_city=city,
            address_country=country,
            is_primary=True,
            consignee=self.consignee,
        )

        BillingAddress.objects.create(
            created_by=user,
            address_street_1=address_street_1,
            address_street_2=address_street_2,
            address_postal_code=address_postal_code,
            address_city=city,
            address_country=country,
            is_primary=True,
            consignee=self.consignee,
        )

    def _prepare_shipping_information(self, ship_to_dict):
        """
        To prepare the shipping information from ship_to_dict
        """
        combined_address = ""
        address = ship_to_dict["address"]

        self.shipping_phone = ""
        self.shipping_mobile = ""
        self.shipping_attn = address["name"].upper()

        # shipping_address
        if "streetAddressOne" in address:
            combined_address += address["streetAddressOne"]
        if "streetAddressTwo" in address:
            combined_address += f", {address['streetAddressTwo']}"
        if "streetAddressThree" in address:
            combined_address += f", {address['streetAddressThree']}"
        if "postalCode" in address:
            combined_address += f", {address['postalCode']}"
        if "city" in address:
            combined_address += f", {address['city']}"

        self.shipping_address = combined_address

        if "contact" in ship_to_dict and ship_to_dict["contact"] is not None:
            if isinstance(ship_to_dict["contact"]["communicationChannel"], list):
                for communicationchannel_dict in ship_to_dict["contact"]["communicationChannel"]:
                    if communicationchannel_dict["communicationChannelCode"] == "TELEPHONE":
                        self.shipping_mobile = communicationchannel_dict["communicationValue"]
                    if communicationchannel_dict["communicationChannelCode"] == "TELEFAX":
                        self.shipping_phone = communicationchannel_dict["communicationValue"]
                    if communicationchannel_dict["communicationChannelCode"] == "EMAIL":
                        self.shipping_attn = communicationchannel_dict["communicationValue"]

            elif isinstance(ship_to_dict["contact"]["communicationChannel"], dict):
                communicationchannel_dict = ship_to_dict["contact"]["communicationChannel"]
                if communicationchannel_dict["communicationChannelCode"] == "TELEPHONE":
                    self.shipping_mobile = communicationchannel_dict["communicationValue"]
                if communicationchannel_dict["communicationChannelCode"] == "TELEFAX":
                    self.shipping_phone = communicationchannel_dict["communicationValue"]
                if communicationchannel_dict["communicationChannelCode"] == "EMAIL":
                    self.shipping_attn = communicationchannel_dict["communicationValue"]

    def _check_file_exist(self):
        """check if file exist in db"""
        filename = get_valid_filename(self.file.name)
        return WarehouseReleaseOrder.objects.filter(imported_outbound_file=f"{UPLOAD_PATH}/{filename}")

    def _check_valid_xml_file(self):
        import xml.etree.ElementTree as ET
        from xml.etree.ElementTree import ParseError

        try:
            ET.parse(self.file)
            return True
        except ParseError:
            return False

    def _check_my_ordered_dict_main_keys(self, my_ordered_dict):
        """check my_ordered_dict's exist despatch_advice:despatchAdviceMessage key or not"""
        if "despatch_advice:despatchAdviceMessage" in my_ordered_dict:
            if "despatchAdvice" in my_ordered_dict["despatch_advice:despatchAdviceMessage"]:
                self.despatch_advice = my_ordered_dict["despatch_advice:despatchAdviceMessage"]["despatchAdvice"]
                if "despatchAdviceLogisticUnit" in self.despatch_advice:
                    if "despatchAdviceIdentification" in self.despatch_advice:
                        if "despatchAdviceLineItem" in self.despatch_advice["despatchAdviceLogisticUnit"]:
                            if "entityIdentification" in self.despatch_advice["despatchAdviceIdentification"]:
                                despatch_advice_id = self.despatch_advice["despatchAdviceIdentification"]
                                self.outbound_delivery_no = despatch_advice_id["entityIdentification"]
                                return True
                        else:
                            error_message = "despatchAdviceLineItem tag is missing"
                            self.error_messages_list.append(error_message)
                            return False
                    else:
                        error_message = "despatchAdviceIdentification tag is missing"
                        self.error_messages_list.append(error_message)
                        return False
                else:
                    error_message = "despatchAdviceLogisticUnit tag is missing"
                    self.error_messages_list.append(error_message)
                    return False
            else:
                error_message = "despatchAdvice tag is missing"
                self.error_messages_list.append(error_message)
                return False
        else:
            error_message = "despatch_advice:despatchAdviceMessage tag is missing"
            self.error_messages_list.append(error_message)
            return False

    def _check_my_ordered_dict_shipping_keys(self, my_ordered_dict, user):
        """check my_ordered_dict's exist despatch_advice:despatchAdviceMessage key or not"""
        despatch_advice = my_ordered_dict["despatch_advice:despatchAdviceMessage"]["despatchAdvice"]
        if "shipTo" in despatch_advice:
            if "address" in despatch_advice["shipTo"]:
                address = despatch_advice["shipTo"]["address"]
                if "name" in address and "countryCode" in address:
                    try:
                        postal_code = str(int(address.get("postalCode", None))).strip()
                    except Exception as e:
                        error_message = f"something wrong with shipTo address's postalCode tag: {e}"
                        self.error_messages_list.append(error_message)
                        return False

                    consignees = Consignee.objects.filter(
                        company_name=address["name"].upper().strip(),
                        consignor=self.consignor,
                    )

                    total_consignees = consignees.count()

                    if total_consignees == 1:
                        self.consignee = consignees[0]
                        self._prepare_shipping_information(despatch_advice["shipTo"])
                    elif total_consignees < 1:
                        # No consignee, create it
                        self._create_new_consignee(address, user, postal_code)
                        self._prepare_shipping_information(despatch_advice["shipTo"])
                        # error_message = f"There are no consignee name [{address['name']}] in Sinoflex System"
                        # self.error_messages_list.append(error_message)
                    else:
                        # More than 1 consignee
                        xml_city = address["city"].strip()
                        xml_postal_code = postal_code
                        consignees = consignees.filter(
                            shippingaddress__address_postal_code=xml_postal_code,
                            shippingaddress__address_city__iexact=xml_city,
                        )

                        total_consignees = consignees.count()

                        if total_consignees == 1:
                            self.consignee = consignees[0]
                            self._prepare_shipping_information(despatch_advice["shipTo"])
                        elif total_consignees < 1:
                            # No consignee, create it
                            self._create_new_consignee(address, user, postal_code)
                            self._prepare_shipping_information(despatch_advice["shipTo"])
                            # error_message = f"There are no consignee name [{address['name']}] in Sinoflex System"
                            # self.error_messages_list.append(error_message)
                        else:
                            # More than 1 consignee with same name, city and postal code
                            # Temporary use the first consignee
                            self.consignee = consignees[0]
                            self._prepare_shipping_information(despatch_advice["shipTo"])
                            # error_message = "Multiple same consignee name, city and postal code"
                            # self.error_messages_list.append(error_message)
                            # return False

                    return True

                else:
                    error_message = "something wrong with shipTo address's tag"
                    self.error_messages_list.append(error_message)
                    return False
            else:
                error_message = "address tag is missing"
                self.error_messages_list.append(error_message)
                return False
        else:
            error_message = "shipTo tag is missing"
            self.error_messages_list.append(error_message)
            return False

    def _check_pick_up_location_keys(self, my_ordered_dict):
        """check my_ordered_dict's exist pickUpLocation key or not"""
        # despatch_advice = my_ordered_dict["despatch_advice:despatchAdviceMessage"]["despatchAdvice"]
        if "pickUpLocation" in self.despatch_advice:
            if "additionalPartyIdentification" in self.despatch_advice["pickUpLocation"]:
                additional_party_id_dict = self.despatch_advice["pickUpLocation"]["additionalPartyIdentification"]

                if "@additionalPartyIdentificationTypeCode" in additional_party_id_dict:
                    if "#text" in additional_party_id_dict:
                        self.fmc_naming_warehouse = additional_party_id_dict["#text"]
                        return True
                    else:
                        error_message = "#text key is missing at pickUpLocation tag"
                        self.error_messages_list.append(error_message)
                        return False
                else:
                    error_message = "@additionalPartyIdentificationTypeCode key is missing at pickUpLocation tag"
                    self.error_messages_list.append(error_message)
                    return False
            else:
                error_message = "additionalPartyIdentification key is missing at pickUpLocation tag"
                self.error_messages_list.append(error_message)
                return False
        else:
            error_message = "pickUpLocation tag is missing"
            self.error_messages_list.append(error_message)
            return False

    def _prepare_pre_batch_split_checking_dict(
        self,
        line_item_number,
        internal_use_3_key_exist,
        internal_use_3_key_value,
        item_code,
        item_obj,
        uom_obj,
        despatched_quantity,
        batch_no,
        expiry_date,
        is_serial_no,
    ):
        """
        expected to prepare dictionary as below:

        self.pre_batch_split_checking_dict = {
            "2845091": [
                {
                    "line_item_number": "000010"
                    "internal_use_3_key_exist": False,
                    "internal_use_3_key_value": "" or pointing to parent's "lineItemNumber" e.g: "000010"
                    "item_code": "2845091",
                    "item_obj": ItemObject,
                    "uom_obj": UomObject,
                    "despatched_quantity": 100,
                    "batch_no": "", either empty if it's a parent or having str value e.g: "D7DA21100"
                    "expiry_date": "", either empty if it's a parent or having str value e.g: "2023-07-28"
                    "is_serial_no": True/False
                },
                {
                    "line_item_number": "900001"
                    "internal_use_3_key_exist": True,
                    "internal_use_3_key_value": "000010"
                    "item_code": "2845091",
                    "item_obj": ItemObject,
                    "uom_obj": UomObject,
                    "despatched_quantity": 100,
                    "batch_no": "", either empty if it's a parent or having str value e.g: "D7DA21100"
                    "expiry_date": "", either empty if it's a parent or having str value e.g: "2023-07-28"
                    "is_serial_no": True/False
                },
                {
                    "line_item_number": "900003"
                    "internal_use_3_key_exist": True,
                    "internal_use_3_key_value": "000010"
                    "item_code": "2845091",
                    "item_obj": ItemObject,
                    "uom_obj": UomObject,
                    "despatched_quantity": 100,
                    "batch_no": "", either empty if it's a parent or having str value e.g: "D7DA21100"
                    "expiry_date": "", either empty if it's a parent or having str value e.g: "2023-07-28"
                    "is_serial_no": True/False
                }
            ],
            "AP00245": [
                {
                    "line_item_number": "000020"
                    "internal_use_3_key_exist": False,
                    "internal_use_3_key_value": "" or pointing to parent's "lineItemNumber"
                    "item_code": "AP00245",
                    "item_obj": ItemObject,
                    "uom_obj": UomObject,
                    "despatched_quantity": 100,
                    "batch_no": "", either empty if it's a parent or having str value e.g: "D7DA21100"
                    "expiry_date": "", either empty if it's a parent or having str value e.g: "2023-07-28"
                    "is_serial_no": True/False
                },
            ]
        }
        """
        batch_split_prepared_dict = {
            "line_item_number": line_item_number,
            "internal_use_3_key_exist": internal_use_3_key_exist,
            "internal_use_3_key_value": internal_use_3_key_value,
            "item_code": item_code,
            "item_obj": item_obj,
            "uom_obj": uom_obj,
            "despatched_quantity": despatched_quantity,
            "batch_no": batch_no,
            "expiry_date": expiry_date,
            "is_serial_no": is_serial_no,
        }

        if item_code in self.pre_batch_split_checking_dict:
            self.pre_batch_split_checking_dict[item_code].append(batch_split_prepared_dict)
        else:
            self.pre_batch_split_checking_dict[item_code] = [batch_split_prepared_dict]

    def _check_item_dict_keys_and_prepare_pre_batch_split_checking_dict(self, item_dict):
        """
        This was the latest version of sample xml structure given by FMC
        can check on prod server AS2 message objects's OUT_079f390ba9d311ee850b0000002f643c.xml for structure

        * 2nd Jan 2024

        expected to check every loop inside the item_list is valid, then prepare a more pre batch split checking dict
        """

        line_item_number = item_dict["lineItemNumber"]
        internal_use_3_key_exist = False
        internal_use_3_key_value = ""
        internal_use_10_key_exist = False
        item_obj = None
        uom_obj = None
        item_code = None
        despatched_quantity = None
        batch_no = "N/A"
        expiry_date = None
        is_serial_no = False

        # checking keys in dict or not
        if "transactionalTradeItem" in item_dict:
            if "transactionalItemData" in item_dict["transactionalTradeItem"]:
                # checking keys in dict or not
                if "additionalTradeItemIdentification" in item_dict["transactionalTradeItem"]:
                    transactional_trade_item = item_dict["transactionalTradeItem"]
                    additional_trade_item_id_list = transactional_trade_item["additionalTradeItemIdentification"]

                    # checking dict in additionalTradeItemIdentification list or not
                    if any(additional_trade_item_id_list) is True:
                        # checking keys in dict or not, and finally retrieved item_code
                        for additional_trade_item_id_dict in additional_trade_item_id_list:
                            if (
                                "@additionalTradeItemIdentificationTypeCode" in additional_trade_item_id_dict
                                and "#text" in additional_trade_item_id_dict
                            ):
                                id_type = additional_trade_item_id_dict["@additionalTradeItemIdentificationTypeCode"]
                                id_text = additional_trade_item_id_dict["#text"]

                                if id_type == "FOR_INTERNAL_USE_3":
                                    internal_use_3_key_exist = True
                                    internal_use_3_key_value = id_text

                                if id_type == "SUPPLIER_ASSIGNED":
                                    item_code = id_text

                                if id_type == "FOR_INTERNAL_USE_10":
                                    self.location = str(id_text)
                                    internal_use_10_key_exist = True
                            else:
                                error_message = (
                                    "@additionalTradeItemIdentificationTypeCode or #text tag missing at "
                                    f"item line {line_item_number}"
                                )
                                self.error_messages_list.append(error_message)
                                return False

                        # Update dictionary to store the internal_use_3_key value for each
                        # line_item_number grouped by item_code, used for checking on bypass items
                        if item_code in self.item_key_by_item_code:
                            self.item_key_by_item_code[item_code].update({line_item_number: internal_use_3_key_exist})
                        else:
                            self.item_key_by_item_code[item_code] = {line_item_number: internal_use_3_key_exist}

                        # if internal_use_10_key_exist is false then Skip this particular despatchAdviceLineItem
                        # it is most probably the 3-levels parent level,
                        # dont need to check any logic or even prepare dict on this
                        if internal_use_10_key_exist is False:
                            return True

                        # if despatched_quantity is 0 then Skip this particular despatchAdviceLineItem
                        # it is most probably the 3-levels parent level, but current logic is on 2nd level parent
                        # dont need to check any logic or even prepare dict on this
                        uom_str = item_dict["despatchedQuantity"]["@measurementUnitCode"]
                        despatched_quantity = item_dict["despatchedQuantity"]["#text"]
                        if Decimal(despatched_quantity) == Decimal(0):
                            return True

                        # check if variable item_code has any value
                        if item_code is None:
                            error_message = f"Missing item/material code at item line {line_item_number}"
                            self.error_messages_list.append(error_message)
                            return False

                        # try to get item object and UOM object from this fragments
                        try:
                            item_obj = Item.objects.get(code=item_code)

                            uom_exist_in_item = False

                            # NOTE: item_obj.uom_json should looks something like this:
                            # {
                            #   "uom": [
                            #     {
                            #       "uom": "PCE",
                            #       "code": "AP00245",
                            #       "name": "HD PART A 10L CON CA 1.25 MMOL1/1  (FME 2ASTOD)",
                            #       "line_number": "00001",
                            #       "conversion_rate_to_pce": "1"
                            #     }
                            #   ],
                            #   "code": "AP00245",
                            #   "name": "HD PART A 10L CON CA 1.25 MMOL1/1  (FME 2ASTOD)",
                            #   "filename": "",
                            #   "as2_message_pk": ""
                            # }
                            if "uom" in item_obj.uom_json:
                                for uom_dict in item_obj.uom_json["uom"]:
                                    if uom_dict["uom"] == uom_str:
                                        uom_conversion_rate_to_pce = uom_dict["conversion_rate_to_pce"]
                                        uom_exist_in_item = True

                                        try:
                                            prepared_symbol = f"{uom_str}-{uom_conversion_rate_to_pce}"
                                            if uom_str == "PCE":
                                                prepared_symbol = "PCE"
                                            elif uom_str == "EA":
                                                prepared_symbol = "EA"

                                            uom_obj = UnitOfMeasure.objects.get(symbol=prepared_symbol)

                                        except UnitOfMeasure.DoesNotExist:
                                            error_message = f"Invalid UOM at item line {line_item_number}"
                                            self.error_messages_list.append(error_message)
                                            return False

                                if uom_exist_in_item is False:
                                    error_message = (
                                        f"The UOM at item line {line_item_number} does not exist "
                                        f"in Sinoflex System, please check on '{item_obj}' material master profile."
                                    )
                                    self.error_messages_list.append(error_message)
                                    return False
                            else:
                                error_message = (
                                    f"The UOM at item line {line_item_number} does not exist "
                                    "in Sinoflex System, please check on material master profile."
                                )
                                self.error_messages_list.append(error_message)
                                return False

                        except Item.DoesNotExist:
                            error_message = f"Invalid item/material code at item line {line_item_number}"
                            self.error_messages_list.append(error_message)
                            # return False

                        # start logics to check on transactional_item_data level
                        transactional_item_data = item_dict["transactionalTradeItem"]["transactionalItemData"]

                        # this condition is to assign batch_no and expiry_date (and handle serialNumber)
                        if isinstance(transactional_item_data, dict):
                            # logic to check whether item's manage type is aligned with what EDI is being sent to us
                            if "batchNumber" in transactional_item_data:
                                if item_obj.manage_type != Item.ManageType.BATCH_MANAGED:
                                    error_message = (
                                        f"Invalid Item Manage Type at item line {line_item_number}"
                                        f"({item_obj.code}) "
                                        f"Expected manage type is {item_obj.get_manage_type_display()}"
                                        ", but given type is Batch Managed. "
                                        f"Requested to release {despatched_quantity} {uom_str}."
                                    )
                                    self.error_messages_list.append(error_message)
                                    return False
                            elif "serialNumber" in transactional_item_data:
                                if item_obj.manage_type != Item.ManageType.SERIAL_MANAGED:
                                    error_message = (
                                        f"Invalid Item Manage Type at item line {line_item_number}"
                                        f"({item_obj.code}) "
                                        f"Expected manage type is {item_obj.get_manage_type_display()}"
                                        ", but given type is Serial Managed. "
                                        f"Requested to release {despatched_quantity} {uom_str}."
                                    )
                                    self.error_messages_list.append(error_message)
                                    return False
                            elif item_obj.manage_type != Item.ManageType.NO_MANAGED:
                                error_message = (
                                    f"Invalid Item Manage Type at item line {line_item_number}"
                                    f"({item_obj.code}) "
                                    f"Expected manage type is {item_obj.get_manage_type_display()}"
                                    ", but given type is Non-Managed. "
                                    f"Requested to release {despatched_quantity} {uom_str}."
                                )
                                self.error_messages_list.append(error_message)
                                return False

                            if "batchNumber" in transactional_item_data:
                                batch_no = transactional_item_data["batchNumber"]
                                serial_split = False
                            elif "serialNumber" in transactional_item_data:
                                """
                                prepare a list of dict of the serialNumber_split scenario
                                serial_no_split_list = [
                                    {
                                        "serial_no": "0BPS2970",
                                        "each_serial_no_qty": 1,
                                    },
                                    {
                                        "serial_no": "0BPS3081",
                                        "each_serial_no_qty": 1,
                                    }
                                ]
                                """
                                serial_no_var = transactional_item_data["serialNumber"]
                                if isinstance(serial_no_var, list):
                                    serial_no_split_list = []
                                    serial_split = True
                                    serial_no_split_count = len(serial_no_var)
                                    each_serial_no_qty = Decimal(despatched_quantity) / Decimal(serial_no_split_count)

                                    for serial_no in serial_no_var:
                                        serial_no_split_dict = {
                                            "serial_no": f"{serial_no}",
                                            "each_serial_no_qty": each_serial_no_qty,
                                        }
                                        serial_no_split_list.append(serial_no_split_dict)
                                else:
                                    serial_split = False
                                    batch_no = transactional_item_data["serialNumber"]

                                is_serial_no = True
                            else:
                                serial_split = False

                        elif isinstance(transactional_item_data, list):
                            serial_split = False

                            for transactional_item_data_dict in transactional_item_data:
                                # logic to check whether item's manage type is aligned with what EDI is being sent to us
                                if "batchNumber" in transactional_item_data_dict:
                                    if item_obj.manage_type != Item.ManageType.BATCH_MANAGED:
                                        error_message = (
                                            f"Invalid Item Manage Type at item line {line_item_number}"
                                            f"({item_obj.code}) "
                                            f"Expected manage type is {item_obj.get_manage_type_display()}, "
                                            "but given type is Batch Managed. "
                                            f"Requested to release {despatched_quantity} {uom_str}."
                                        )
                                        self.error_messages_list.append(error_message)
                                        return False
                                elif "serialNumber" in transactional_item_data_dict:
                                    if item_obj.manage_type != Item.ManageType.SERIAL_MANAGED:
                                        error_message = (
                                            f"Invalid Item Manage Type at item line {line_item_number}"
                                            f"({item_obj.code}) "
                                            f"Expected manage type is {item_obj.get_manage_type_display()}, "
                                            "but given type is Serial Managed. "
                                            f"Requested to release {despatched_quantity} {uom_str}."
                                        )
                                        self.error_messages_list.append(error_message)
                                        return False
                                elif item_obj.manage_type != Item.ManageType.NO_MANAGED:
                                    error_message = (
                                        f"Invalid Item Manage Type at item line {line_item_number}"
                                        f"({item_obj.code}) "
                                        f"Expected manage type is {item_obj.get_manage_type_display()}"
                                        ", but given type is Non-Managed. "
                                        f"Requested to release {despatched_quantity} {uom_str}."
                                    )
                                    self.error_messages_list.append(error_message)
                                    return False

                        if "itemExpirationDate" in transactional_item_data:
                            expiry_date_str = transactional_item_data["itemExpirationDate"]
                            expiry_date = datetime.strptime(expiry_date_str, "%Y-%m-%d").date()

                        # up until this stage, the item_dict is supposed to be a valid item_dict
                        # then now, prepare a pre_batch_split_checking_dict
                        # for later to filter out the "batch split item"

                        if serial_split is True:
                            for serial_no_dict in serial_no_split_list:
                                self._prepare_pre_batch_split_checking_dict(
                                    line_item_number,
                                    True,
                                    internal_use_3_key_value,
                                    item_code,
                                    item_obj,
                                    uom_obj,
                                    serial_no_dict["each_serial_no_qty"],
                                    serial_no_dict["serial_no"],
                                    expiry_date,
                                    is_serial_no,
                                )
                            return True
                        else:
                            self._prepare_pre_batch_split_checking_dict(
                                line_item_number,
                                internal_use_3_key_exist,
                                internal_use_3_key_value,
                                item_code,
                                item_obj,
                                uom_obj,
                                despatched_quantity,
                                batch_no,
                                expiry_date,
                                is_serial_no,
                            )
                            return True
                    else:
                        error_message = (
                            f"No elements in additionalTradeItemIdentification tag at item line {line_item_number}"
                        )
                        self.error_messages_list.append(error_message)
                        return False
                else:
                    error_message = f"No additionalTradeItemIdentification tag at item line {line_item_number}"
                    self.error_messages_list.append(error_message)
                    return False
            else:
                error_message = f"No transactionalItemData tag at item line {line_item_number}"
                self.error_messages_list.append(error_message)
                return False
        else:
            error_message = f"No transactionalTradeItem tag at item line {line_item_number}"
            self.error_messages_list.append(error_message)
            return False

    def _check_key_for_bypass_items(self) -> list:
        """
        Check if the line items could potentially be bypassed due to the batch split items.
        """
        validation_bypass_items = []
        for item_code_key, item_batch_split_list in self.pre_batch_split_checking_dict.items():
            if len(item_batch_split_list) > 1:
                can_bypass = False
                for batch_split_dict in item_batch_split_list:
                    if batch_split_dict["internal_use_3_key_exist"] is True:
                        can_bypass = True

                if can_bypass:
                    item_key_by_item_code = self.item_key_by_item_code.get(item_code_key, {})
                    for line_item_number, internal_use_3_key_exist in item_key_by_item_code.items():
                        if internal_use_3_key_exist is False:
                            validation_bypass_items.append(line_item_number)

        return validation_bypass_items

    def _check_and_filter_my_ordered_dict_item_list_keys(self, my_ordered_dict):
        # depatch_advice = my_ordered_dict["despatch_advice:despatchAdviceMessage"]["despatchAdvice"]
        item_list_or_dict = self.despatch_advice["despatchAdviceLogisticUnit"]["despatchAdviceLineItem"]

        valid = False

        if isinstance(item_list_or_dict, list):
            line_item_valid_dict = {}
            for item_dict in item_list_or_dict:
                item_valid = self._check_item_dict_keys_and_prepare_pre_batch_split_checking_dict(item_dict=item_dict)
                line_item_valid_dict[item_dict.get("lineItemNumber")] = item_valid

            if all(line_item_valid_dict.values()):
                valid = True
            else:
                # If any one of the validations failed, we check also whether the corresponding line item
                # might be a batch split item (where the internal_use_3_key_exist=True for
                # one of the items within the same Item Code).
                validation_bypass_items = self._check_key_for_bypass_items()
                # Since we will eventually filter out the batch split items in our cleaned() process,
                # if only the line items with internal_use_3_key_exist=False fails validation,
                # we will still consider the file as valid.
                filtered_line_item_valid_dict = {
                    k: v for k, v in line_item_valid_dict.items() if k not in validation_bypass_items
                }
                valid = all(filtered_line_item_valid_dict.values())
        elif isinstance(item_list_or_dict, dict):
            valid = self._check_item_dict_keys_and_prepare_pre_batch_split_checking_dict(item_dict=item_list_or_dict)

        return valid

    def is_valid(self, user):
        """Function to check if the uploaded file is valid."""

        valid = True

        # first checking to be done due to first most important thing is the XML file must be a valid file
        # if self._check_valid_xml_file() is False:
        #     error_message = f"The XML file is not valid due to mismatch tag"
        #     self.error_messages_list.append(error_message)
        #     self._valid = valid
        #     return False

        # warehouse_release_order_qs = self._check_file_exist()
        # if warehouse_release_order_qs.exists():
        #     valid = False
        #     error_message = f"File {self.file.name} uploaded in {warehouse_release_order_qs[0].numbering}"
        #     self.error_messages_dict["__all__"] = [error_message]
        #     self.error_messages_list.append(error_message)

        if self.release_datetime is None:
            self.release_datetime = localtime_now() + timedelta(days=settings.FMC_DEFAULT_DO_DUE_DATETIME)

        # read xml content from the file
        xml_content = self.file.read()

        my_ordered_dict = xmltodict.parse(xml_content)
        # print(my_ordered_dict)

        if self._check_my_ordered_dict_main_keys(my_ordered_dict) is False:
            valid = False
        elif self._check_my_ordered_dict_shipping_keys(my_ordered_dict, user) is False:
            valid = False
        elif self._check_pick_up_location_keys(my_ordered_dict) is False:
            valid = False
        elif self._check_and_filter_my_ordered_dict_item_list_keys(my_ordered_dict) is False:
            valid = False

        self._valid = valid

        return self._valid

    def cleaned(self):
        """Function to build cleaned data from uploaded XML file."""

        if self._valid is True:
            # needs to be after _check_pick_up_location_keys function
            if self.warehouses is None:
                if self.fmc_naming_warehouse in settings.FMC_DEFAULT_WRO_WAREHOUSES:
                    sino_warehouse_list = settings.FMC_DEFAULT_WRO_WAREHOUSES[self.fmc_naming_warehouse]
                    parent_warehouse = Warehouse.objects.filter(name=sino_warehouse_list[0])
                    location_warehouse = parent_warehouse.first().get_children().filter(name__icontains=self.location)
                    self.warehouses = location_warehouse
                else:
                    error_message = f"There is no {self.fmc_naming_warehouse} in Sinoflex System."
                    self.error_messages_list.append(error_message)

            # prepare self.cleaned_batch_split_list
            for item_code_key, item_batch_split_list in self.pre_batch_split_checking_dict.items():
                if len(item_batch_split_list) > 1:
                    # before really prepare the cleaned_batch_split_list, check if it is really batch split or not first
                    batch_split_bool = False
                    for batch_split_dict in item_batch_split_list:
                        if batch_split_dict["internal_use_3_key_exist"] is True:
                            batch_split_bool = True

                    if batch_split_bool is True:
                        for batch_split_dict in item_batch_split_list:
                            if batch_split_dict["internal_use_3_key_exist"] is True:
                                self.cleaned_batch_split_list.append(batch_split_dict)
                    else:
                        for batch_split_dict in item_batch_split_list:
                            self.cleaned_batch_split_list.append(batch_split_dict)

                elif len(item_batch_split_list) == 1:
                    self.cleaned_batch_split_list.append(item_batch_split_list[0])

    def process_internal(self, user):
        """To create WarehouseReleaseOrder and WarehouseReleaseOrderItem based on internal cleaned_batch_split_list.

        Example of cleaned_batch_split_list:

        cleaned_batch_split_list = [
            {
                "line_item_number": "000010"
                "internal_use_3_key_exist": False,
                "internal_use_3_key_value": "" or pointing to parent's "lineItemNumber" e.g: "000010"
                "item_code": "2845091",
                "item_obj": ItemObject,
                "uom_obj": UomObject,
                "despatched_quantity": "100",
                "batch_no": "", either empty if it's a parent or having str value e.g: "D7DA21100"
                "expiry_date": "", either empty if it's a parent or having datetime object value e.g: "2023-07-28"
                "is_serial_no": True/False
            },
            {
                "line_item_number": "000020",
                "internal_use_3_key_exist": True,
                "internal_use_3_key_value": "" or pointing to parent's "lineItemNumber" e.g: "000010"
                "item_code": "AP00245",
                "item_obj": ItemObject,
                "uom_obj": UomObject,
                "despatched_quantity": "200",
                "batch_no": "", either empty if it's a parent or having str value e.g: "D7DA21100"
                "expiry_date": "", either empty if it's a parent or having datetime object value e.g: "2023-07-28"
                "is_serial_no": True/False
            },
            ...
        ]

        """
        warehouse_release_order = WarehouseReleaseOrder.objects.create(
            created_by=user,
            issued_by=user,
            consignee=self.consignee,
            release_datetime=self.release_datetime,
            imported_outbound_file=self.file,
            is_delivery_order=True,
            consignor_outbound_delivery_no=self.outbound_delivery_no,
            customer_reference=self.outbound_delivery_no,
            is_from_edi=True,
        )

        warehouse_release_order.shipping_address = self.shipping_address
        warehouse_release_order.shipping_phone = self.shipping_phone
        warehouse_release_order.shipping_mobile = self.shipping_mobile
        warehouse_release_order.shipping_attn = self.shipping_attn
        warehouse_release_order.modified_by = user
        warehouse_release_order.save()

        # temporary only, must fix better error handling on missing warheouse (13/10/2023)
        if self.warehouses:
            for warehouse in self.warehouses:
                warehouse_release_order.warehouses.add(warehouse)
        else:
            temp = Warehouse.objects.get(name="Warehouse KK01")
            warehouse_release_order.warehouses.add(temp)

        for item in self.cleaned_batch_split_list:
            WarehouseReleaseOrderItem.objects.create(
                created_by=user,
                sort_order=int(item["line_item_number"]),
                release_order=warehouse_release_order,
                item=item["item_obj"],
                quantity=Decimal(item["despatched_quantity"]),
                uom=item["uom_obj"],
                batch_no=item["batch_no"],
                expiry_date=item["expiry_date"],
                is_serial_no=item["is_serial_no"],
            )

        existing_duplicated_wro_qs = (
            WarehouseReleaseOrder.objects.filter(
                consignor_outbound_delivery_no=self.outbound_delivery_no, is_from_edi=True, consignee=self.consignee
            )
            .exclude(pk=warehouse_release_order.pk)
            .exclude(
                status__in=[
                    WarehouseReleaseOrder.Status.READY_TO_RELEASE,
                    WarehouseReleaseOrder.Status.OBSOLETE,
                    WarehouseReleaseOrder.Status.COMPLETED,
                ]
            )
        )

        if existing_duplicated_wro_qs:
            involved_picking_list = None

            for existing_duplicated_wro in existing_duplicated_wro_qs:
                existing_duplicated_wro.status = WarehouseReleaseOrder.Status.OBSOLETE

                if existing_duplicated_wro.picking_list:
                    involved_picking_list = existing_duplicated_wro.picking_list
                    existing_duplicated_wro.picking_list = None

                existing_duplicated_wro.save()

            if involved_picking_list:
                warehouse_release_order.picking_list = involved_picking_list
                warehouse_release_order.save()

                involved_picking_list.regenerate_picking_list_item()

        return warehouse_release_order
