{% load i18n %}

{% block content %}
  {% if object %}
    <section class="mx-auto border-1 [&:not(:first-child)]:border-t-0 border-theme-input-border-secondary">
      <div
        class="py-1.5 px-3 border-b-1 min-h-10 bg-theme-table-header border-theme-input-border-secondary font-semibold uppercase tracking-wider flex justify-between items-center">
        <span>{% trans "Basic Information" %}</span>
        <div class="flex gap-4">
          {% if picking_list.status == "New" %}
          <button
            hx-get="{% url 'pickings:picking_lists:delete_form' picking_list.pk %}"
            hx-target="#modal-form-content"
            hx-swap="innerHTML"
            class="inline-flex items-center px-3 py-1 text-sm bg-theme-status-error text-white rounded hover:bg-theme-status-error/80 transition-colors"
            title="{% translate 'Delete Picking List' %}"
            @click="$dispatch('open-modal', { title: '{% trans 'Delete Picking List' %}' })">
            {% trans "Delete" %}
          </button>
          {% endif %}
          {% if picking_list.status == "Completed" %}
            <a href="#"
               target="_blank"
               class="inline-flex items-center justify-center px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition">
              <svg xmlns="http://www.w3.org/2000/svg"
                   class="h-5 w-5 mr-2"
                   viewBox="0 0 24 24"
                   fill="none"
                   stroke="currentColor"
                   stroke-width="2"
                   stroke-linecap="round"
                   stroke-linejoin="round">
                <polyline points="6 9 6 2 18 2 18 9"></polyline>
                <path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"></path>
                <rect x="6" y="14" width="12" height="8"></rect>
              </svg>
              Print Picking List
            </a>
          {% endif %}
        </div>
      </div>
      <div class="bg-white rounded-none overflow-hidden">
        <!-- Main Information Card -->
        <div class="p-0">
          <div class="space-y-6">
            <!-- Picking List Information Section -->
            <div class="bg-theme-bg-secondary/10 rounded-none">
              <div class="grid grid-cols-2">
                <!-- Left Content -->
                <div class="grid grid-cols-1 divide-y divide-gray-200 ">
                  {% include "_components/detail_field.html" with label="System Number" value=picking_list.system_number %}
                  {% include "_components/detail_field.html" with label="Name" value=picking_list.name %}
                  <div
                    class="flex border-b-1 [&:last-child]:border-b-0 [&:only-child]:border-b-1 rounded-none border-gray-200">
                    <label
                      class="flex items-center p-3 justify-end text-end text-theme-text-primary text-sm w-36 font-medium bg-theme-form-label">
                      {% trans "Status" %}
                    </label>
                    <span class="flex items-center p-3 text-theme-text-primary bg-theme-form-span flex-1">
                      {% include "pickings/picking_lists/partials/status_with_complete_icon.html" with value=picking_list.html_status_display %}
                    </span>
                  </div>
                  <div
                    class="flex border-b-1 [&:last-child]:border-b-0 [&:only-child]:border-b-1 rounded-none border-gray-200">
                    <label
                      class="flex items-center p-3 justify-end text-end text-theme-text-primary text-sm w-36 font-medium bg-theme-form-label">
                      {% trans "Release From" %}
                    </label>
                    <span class="flex items-center p-3 text-theme-text-primary bg-theme-form-span flex-1">
                        <span class="flex flex-wrap gap-2">
                        <span
                          title="{{ picking_list.release_from }}"
                          class="badge bg-theme-link">
                          {{ picking_list.release_from }}
                        </span>
                      </span>
                      </span>
                  </div>
                </div>
                <!-- Right Content -->
                <div class="grid grid-cols-1 divide-y divide-gray-200 ">
                  {% include "_components/detail_field.html" with label="Issued By" value=picking_list.issued_by %}
                  {% include "_components/detail_field.html" with label="Expected Date" value=picking_list.expected_completion_date|date:"Y-m-d" %}
                  {% include "_components/detail_field.html" with label="Completion Date" value=picking_list.completion_datetime|date:"Y-m-d g:i A"|default:"-" %}
                  {% include "_components/detail_field.html" with label="Remark" value=picking_list.remark|default:"-" %}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="mx-auto mt-4 border-0 border-theme-input-border-secondary">
    </section>

    <section class="mx-auto mt-4 border-0 border-theme-input-border-secondary">
      <div class="flex justify-end gap-4">
        {% if picking_list.status == "New" %}
          <button
            hx-get="{% url 'pickings:picking_lists:add_release_order' picking_list.pk %}?hxf=1"
            hx-target="#modal-form-content"
            hx-swap="innerHTML"
            class="inline-flex items-center justify-center px-2 py-1 bg-theme-primary text-white rounded hover:bg-theme-primary-hover transition"
            title="{% translate 'Add Release Orders' %}"
            @click="$dispatch('open-modal', { title: '{% trans 'Add Release Orders' %}' })">
            <svg xmlns="http://www.w3.org/2000/svg"
                 class="h-5 w-5 mr-2"
                 viewBox="0 0 24 24"
                 fill="none"
                 stroke="currentColor"
                 stroke-width="2"
                 stroke-linecap="round"
                 stroke-linejoin="round">
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            {% translate "Add Release Orders" %}
          </button>
        {% endif %}
      </div>
      <div class="bg-white overflow-hidden">
        <!-- partial table content -->
        <div hx-get="{% url 'pickings:picking_lists:release_order_list' picking_list.pk %}?hxf=1"
             hx-trigger="load"
             hx-swap="outerHTML">{% include '_components/loading_spinner.html' %}</div>
      </div>
    </section>

    <section class="mx-auto mt-4 border-0 border-theme-input-border-secondary">
      <div class="flex justify-end gap-4">
        {% if picking_list.status == "New" or picking_list.status == "Processing" %}
          <form method="post"
                hx-post="{% url 'pickings:picking_lists:picking_list_assign_rack' picking_list.pk %}"
                class="ml-2">
            {% csrf_token %}
            <button type="submit"
                class="inline-flex items-center justify-center px-2 py-1 bg-theme-primary text-white rounded hover:bg-theme-primary-hover transition"
                title="{% translate 'Auto-Assign Rack' %}">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="-5 w-5 mr-2">
                <path stroke-linecap="round" stroke-linejoin="round" d="m5.25 4.5 7.5 7.5-7.5 7.5m6-15 7.5 7.5-7.5 7.5" />
              </svg>
              {% translate "Auto-Assign Rack" %}
            </button>
          </form>
        {% endif %}
      </div>
      <div class="bg-white overflow-hidden">
        <!-- partial table content -->
        <div hx-get="{% url 'pickings:picking_lists:item_list' picking_list.pk %}?hxf=1"
             hx-trigger="load"
             hx-swap="outerHTML">{% include '_components/loading_spinner.html' %}</div>
      </div>
    </section>
  {% endif %}
{% endblock content %}
