{% load custom_tags %}
{% get_user_menus as menu_data %}

<div class="h-12 border-b border-theme-border-secondary bg-white px-6 flex items-center justify-between">
  <!-- Left side with toggle -->
  <div class="flex items-center gap-4">
    <button title="title"
            @click="$store.sidebar.toggle()"
            :class="{'hidden': $store.sidebar.isOpen}"
            class="p-2 rounded-lg hover:bg-theme-hover transition-colors duration-300">
      <svg class="w-5 h-5 text-theme-text-secondary"
           fill="none"
           stroke="currentColor"
           viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              :d="!$store.sidebar.isOpen && 'M4 6h16M4 12h16M4 18h16'"/>
      </svg>
    </button>
    <!-- Enhanced Breadcrumbs -->
    <nav class="flex items-center text-sm">
      <!-- Breadcrumbs content -->

      {% if custom_breadcrumb %}
        <!-- Custom breadcrumb for views without standard menu structure -->
        {% if custom_breadcrumb.path %}
          <!-- Hierarchical path-based breadcrumb -->
          {% for item in custom_breadcrumb.path %}
            {% if not forloop.first %}
              <span class="text-theme-border-secondary px-2">/</span>
            {% endif %}

            {% if item.url %}
              <a href="{{ item.url }}" class="text-theme-link hover:text-theme-link-hover transition-colors">
                {{ item.name }}
              </a>
            {% else %}
              <span class="text-theme-text-primary">{{ item.name }}</span>
            {% endif %}
          {% endfor %}
        {% else %}
          <!-- Simple module/submenu breadcrumb -->
          {% if custom_breadcrumb.module_url %}
            <a href="{{ custom_breadcrumb.module_url }}"
               class="text-theme-link hover:text-theme-link-hover transition-colors">
              {{ custom_breadcrumb.module|title }}
            </a>
          {% else %}
            <span class="text-theme-text-primary">{{ custom_breadcrumb.module|title }}</span>
          {% endif %}

          <span class="text-theme-border-secondary px-2">/</span>

          {% if custom_breadcrumb.submenu_url %}
            <a href="{{ custom_breadcrumb.submenu_url }}"
               class="text-theme-link hover:text-theme-link-hover transition-colors">
              {{ custom_breadcrumb.submenu|title }}
            </a>
          {% else %}
            <span class="text-theme-text-primary">{{ custom_breadcrumb.submenu|title }}</span>
          {% endif %}

          <span class="text-theme-border-secondary px-2">/</span>
          <span class="text-theme-text-primary">{{ custom_breadcrumb.view_type }}</span>

          {% if custom_breadcrumb.object_identifier %}
            <span class="text-theme-border-secondary px-2">/</span>
            <a href="{{ request.path_info }}"
               class="text-theme-link font-medium hover:text-theme-link-hover transition-colors">
              {{ custom_breadcrumb.object_identifier }}
            </a>
          {% endif %}
        {% endif %}
      {% elif menu_data.active_module %}
        {% if menu_data.active_submenu %}
          <!-- Module with submenu (not clickable) -->
          <span class="text-theme-text-primary">{{ menu_data.active_module|title }}</span>
        {% else %}
          <!-- Module without submenu (clickable) -->
          {% for module in menu_data.menus %}
            {% if module.label == menu_data.active_module %}
              <a href="{{ module.url }}"
                 class="text-theme-link hover:text-theme-link-hover transition-colors">
                {{ menu_data.active_module|title }}
              </a>

              <!-- Add view type for modules without submenus -->
              {% with url_name=request.resolver_match.url_name %}
                <span class="text-theme-border-secondary px-2">/</span>
                {% if 'create' in url_name %}
                  <span class="text-theme-text-primary">Create</span>
                {% elif 'update' in url_name %}
                  <span class="text-theme-text-primary">Update</span>

                  <span id="breadcrumb-object-identifier-update">
                    {% if object_identifier %}
                      <span class="text-theme-border-secondary px-2">/</span>
                      <a href="{{ request.path_info }}"
                         class="text-theme-link font-medium hover:text-theme-link-hover transition-colors">
                        {{ object_identifier }}
                      </a>
                    {% endif %}
                  </span>
                {% elif 'detail' in url_name or 'panel' in url_name %}
                  <span class="text-theme-text-primary">Detail</span>

                  <span id="breadcrumb-object-identifier-detail-module">
                    {% if object_identifier %}
                      <span class="text-theme-border-secondary px-2">/</span>
                      <a href="{{ request.path_info }}"
                         class="text-theme-link font-medium hover:text-theme-link-hover transition-colors">
                        {{ object_identifier }}
                      </a>
                    {% endif %}
                  </span>
                {% else %}
                  <span class="text-theme-text-primary">List</span>
                {% endif %}
              {% endwith %}
            {% endif %}
          {% endfor %}
        {% endif %}

        {% if menu_data.active_submenu %}
          <span class="text-theme-border-secondary px-2">/</span>
          <!-- Submenu (clickable) -->
          {% for module in menu_data.menus %}
            {% if module.label == menu_data.active_module %}
              {% for submenu in module.submenus %}
                {% if submenu.label == menu_data.active_submenu %}
                  <a href="{{ submenu.url }}"
                     class="text-theme-link hover:text-theme-link-hover transition-colors">
                    {{ menu_data.active_submenu|title }}
                  </a>
                {% endif %}
              {% endfor %}
            {% endif %}
          {% endfor %}

          <!-- Detect view type based on URL pattern -->
          {% with url_name=request.resolver_match.url_name %}
            <span class="text-theme-border-secondary px-2">/</span>
            {% if 'create' in url_name %}
              <span class="text-theme-text-primary">Create</span>
            {% elif 'update' in url_name %}
              <span class="text-theme-text-primary">Update</span>

              <span id="breadcrumb-object-identifier-update-main">
                {% if object_identifier %}
                  <span class="text-theme-border-secondary px-2">/</span>
                  <a href="{{ request.path_info }}"
                     class="text-theme-link font-medium hover:text-theme-link-hover transition-colors">
                    {{ object_identifier }}
                  </a>
                {% endif %}
              </span>
            {% elif 'detail' in url_name or 'panel' in url_name %}
              <span class="text-theme-text-primary">Detail</span>

              <span id="breadcrumb-object-identifier-detail">
                {% if object_identifier %}
                  <span class="text-theme-border-secondary px-2">/</span>
                  <a href="{{ request.path_info }}"
                     class="text-theme-link font-medium hover:text-theme-link-hover transition-colors">
                    {{ object_identifier }}
                  </a>
                {% endif %}
              </span>
            {% else %}
              <span class="text-theme-text-primary">List</span>
            {% endif %}
          {% endwith %}
        {% endif %}
      {% else %}
        <!-- Fallback when active_module is None -->
        {% for module in menu_data.menus %}
          {% for submenu in module.submenus %}
            {% if submenu.active %}
              <span class="text-theme-text-primary">{{ module.label|title }}</span>
              <span class="text-theme-border-secondary px-2">/</span>
              <a href="{{ submenu.url }}"
                 class="text-theme-link hover:text-theme-link-hover transition-colors">
                {{ submenu.label|title }}
              </a>
              <!-- Detect view type based on URL pattern -->
              {% with url_name=request.resolver_match.url_name %}
                <span class="text-theme-border-secondary px-2">/</span>
                {% if 'create' in url_name %}
                  <span class="text-theme-text-primary">Create</span>
                {% elif 'update' in url_name %}
                  <span class="text-theme-text-primary">Update</span>

                  {% if object_identifier %}
                    <span class="text-theme-border-secondary px-2">/</span>
                    <a href="{{ request.path_info }}"
                       class="text-theme-link font-medium hover:text-theme-link-hover transition-colors">
                      {{ object_identifier }}
                    </a>
                  {% endif %}
                {% elif 'detail' in url_name or 'panel' in url_name %}
                  <span class="text-theme-text-primary">Detail</span>

                  <span id="breadcrumb-object-identifier-fallback">
                    {% if object_identifier %}
                      <span class="text-theme-border-secondary px-2">/</span>
                      <a href="{{ request.path_info }}"
                         class="text-theme-link font-medium hover:text-theme-link-hover transition-colors">
                        {{ object_identifier }}
                      </a>
                    {% endif %}
                  </span>
                {% else %}
                  <span class="text-theme-text-primary">List</span>
                {% endif %}
              {% endwith %}
            {% endif %}
          {% endfor %}
        {% endfor %}
      {% endif %}

    </nav>
  </div>
  <div class="flex justify-end">
    <form method="post" action="{% url 'users:update_consignor_filter' %}" style="margin: 0; padding: 0;">
      {% csrf_token %}
      <div class="flex items-center mr-4 gap-2">
        {{ consignor_filter_form.consignor_filter }}
      </div>
    </form>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const consignorSelect = $('#id_consignor_filter');
    const form = document.querySelector('form[action="{% url 'users:update_consignor_filter' %}"]');
    if (consignorSelect.length) {
      // Add our custom handler
      consignorSelect.on('select2:select select2:unselect', function(e) {
        form.submit();
      });
    }
  });
</script>


