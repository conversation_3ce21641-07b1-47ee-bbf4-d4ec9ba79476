{% load i18n %}
{% load crispy_forms_tags %}

<form hx-post="{% url 'releases:orders:item_rack_reassign' record.pk %}"
      hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
      class="space-y-4"
      id="rack-reassign-form">
  {% csrf_token %}

  <div class="space-y-4">
    <div>
      <h3 class="text-lg font-medium">{% translate "Reassign Rack" %}</h3>
      <p class="text-sm text-theme-text-secondary">
        {% translate "Select which rack to reassign from, which rack to reassign to, and the quantity to reassign." %}
      </p>
    </div>

    <div class="grid grid-cols-1 gap-4">
      {{ form|crispy }}
    </div>

    <div class="flex justify-end space-x-2">
      <button type="button"
              class="btn btn-secondary"
              @click="$dispatch('close-modal')">
        {% translate "Cancel" %}
      </button>
      <button type="submit"
              class="btn btn-primary">
        {% translate "Reassign" %}
      </button>
    </div>
  </div>
</form>

<script>
  (function() {
    // Function to filter out the selected "From Rack" from the "To Rack" options
    function setupRackSelectionFiltering() {
      const fromRackSelect = document.getElementById('id_from_rack_storage');
      const toRackSelect = document.getElementById('id_to_rack_storage');

      if (!fromRackSelect || !toRackSelect) return;

      // Store the original options of the "To Rack" dropdown
      let originalToRackOptions = [];
      for (let i = 0; i < toRackSelect.options.length; i++) {
        originalToRackOptions.push({
          value: toRackSelect.options[i].value,
          text: toRackSelect.options[i].text
        });
      }

      // Function to update "To Rack" options based on "From Rack" selection
      function updateToRackOptions() {
        const selectedFromRackValue = fromRackSelect.value;

        // Destroy the current Select2 instance
        $(toRackSelect).select2('destroy');

        // Clear all options
        toRackSelect.innerHTML = '';

        // Add back all options except the one selected in "From Rack"
        originalToRackOptions.forEach(option => {
          if (option.value !== selectedFromRackValue) {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.text = option.text;
            toRackSelect.appendChild(optionElement);
          }
        });

        // Reinitialize Select2
        window.initializeSelect2(toRackSelect);
      }

      // Set up event listener for "From Rack" changes
      $(fromRackSelect).on('change', updateToRackOptions);

      // Initial filtering
      updateToRackOptions();
    }

    // Set up the filtering when the form is loaded
    document.addEventListener('htmx:afterSettle', function() {
      setupRackSelectionFiltering();
    });

    // Also set up immediately in case the form is already loaded
    setupRackSelectionFiltering();
  })();
</script>
