{% load i18n %}

<div class="flex items-center gap-2 justify-center">
  {% if record.is_freezed %}
    <button
      hx-get="{% url 'inventories:rack_transaction_unfreeze_form' record.pk %}"
      hx-target="#modal-form-content"
      hx-swap="innerHTML"
      class="p-2 text-red-600 hover:text-red-800 cursor-pointer"
      title="{% translate 'Unfreeze' %}"
      @click="$dispatch('open-modal', { title: '{% trans 'Unfreeze Transaction' %}' })">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none"
           stroke="currentColor"
           stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M18 6L6 18"></path>
        <path d="M6 6l12 12"></path>
      </svg>
    </button>
  {% endif %}
</div>
