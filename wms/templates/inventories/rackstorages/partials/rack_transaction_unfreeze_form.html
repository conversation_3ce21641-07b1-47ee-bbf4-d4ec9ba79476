{% load i18n %}
{% load crispy_forms_tags %}

<form
  method="post"
  hx-post="{% url 'inventories:rack_transaction_unfreeze' rack_transaction.pk %}"
  hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
>
  {% csrf_token %}
  <div class="p-4">
    <!-- Transaction Information Section -->
    <div class="mb-4 border border-theme-border-primary rounded-xs overflow-hidden">
      <div class="bg-theme-bg-secondary px-4 py-1.5 border-b border-theme-border-primary">
        <h4 class="text-sm font-medium text-theme-text-primary">{% trans "Transaction Details" %}</h4>
      </div>
      <div class="p-4">
        <!-- Basic Transaction Information -->
        <div class="grid grid-cols-2 gap-3 sm:grid-cols-2 mb-4">
          <div class="col-span-2">
            <label class="block text-sm font-medium text-gray-700">{% trans "Rack" %}</label>
            <div class="mt-1 text-sm text-gray-900 font-semibold">{{ rack_transaction.rackstorage.rack.full_name }}</div>
          </div>
          <div class="col-span-2">
            <label class="block text-sm font-medium text-gray-700">{% trans "Item" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ rack_transaction.rackstorage.stock.item.code }} - {{ rack_transaction.rackstorage.stock.item.name }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Quantity" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ rack_transaction.quantity }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700">{% trans "Transaction Date" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ rack_transaction.transaction_datetime|date:"d M Y H:i" }}</div>
          </div>
          {% if rack_transaction.warehouse_release_order_item %}
          <div class="col-span-2">
            <label class="block text-sm font-medium text-gray-700">{% trans "Release Order Item" %}</label>
            <div class="mt-1 text-sm text-gray-900">{{ rack_transaction.warehouse_release_order_item }}</div>
          </div>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Unfreeze Confirmation -->
    <div class="mb-4 text-center">
      <h3 class="text-lg font-medium text-red-600">
        {% translate "Are you sure you want to unfreeze this transaction?" %}
      </h3>
      <p class="mt-2 text-sm text-gray-500">
        {% translate "This will delete the transaction record. This action cannot be undone." %}
      </p>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-between space-x-3">
      <button
        type="button"
        class="py-1.5 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400"
        @click="$dispatch('close-modal')"
      >
        {% translate "Cancel" %}
      </button>
      <button
        type="submit"
        class="py-1.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
      >
        {% translate "Unfreeze" %}
      </button>
    </div>
  </div>
</form>
