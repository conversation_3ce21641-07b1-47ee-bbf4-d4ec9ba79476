{% extends "base.html" %}

{% load crispy_forms_tags %}
{% load i18n %}

{% block content %}
  <form method="post">
    {% csrf_token %}
    <section class="max-w-4xl mx-auto py-8">
      <div class="mb-6">
        <h1 class="text-2xl font-semibold text-theme-text-primary">{{ section_title }}</h1>
        {% if section_desc %}<p class="mt-2 text-theme-text-secondary">{{ section_desc }}</p>{% endif %}
      </div>
      {% include 'tailwind/layout/form_errors_block.html' %}
      {% include '_components/color_bar.html' %}
      <section class="mx-auto border-1 border-gray-200">
        {% include '_components/section_header.html' with header_title="Basic Information" %}
        <div class="bg-white rounded-none overflow-hidden">
          <div class="p-0">
            <div class="space-y-6">
              <div class="bg-theme-bg-secondary/10 rounded-none">
                <div class="grid grid-cols-1 divide-y divide-gray-200">
                  <!-- Loop through form fields -->
                  {{ form.type|as_crispy_field }}
                  {{ form.rackstorage|as_crispy_field }}
                  {{ form.transaction_datetime|as_crispy_field }}
                  {{ form.quantity|as_crispy_field }}
                  {{ form.remark|as_crispy_field }}
                </div>
              </div>
            </div>
          </div>
        </section>
        <div class="flex items-center justify-between mt-4">
          {% block form_buttons %}
            {% include "_components/button_cancel.html" with link=cancel_url text=cancel_text|default:"Cancel" %}
            {% include "_components/button_submit.html" with action_type=action_type text=submit_text|default:"Save" %}
          {% endblock form_buttons %}
        </div>
      </section>
    </form>
  {% endblock content %}
