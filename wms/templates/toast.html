<!-- toast.html -->
<div x-data="toast"
     @notify.window="show($event.detail)"
     class="fixed top-12 right-10 z-50 flex flex-col gap-4 w-full max-w-md sm:max-w-xl"
     role="status"
     aria-live="polite">
  <template x-for="(toast, index) in toasts" :key="index">
    <div x-show="toast.visible"
         x-transition:enter="transform ease-out duration-300 transition"
         x-transition:enter-start="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-4"
         x-transition:enter-end="translate-y-0 opacity-100 sm:translate-x-0"
         x-transition:leave="transition ease-in duration-100"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="flex items-center p-2 mt-2 rounded-xs"
         :class="toast && toast.type === 'success' ? 'bg-theme-status-success/20 text-theme-status-success border border-theme-status-success/30' : toast && toast.type === 'error' ? 'bg-theme-status-error/20 text-theme-status-error border border-theme-status-error/30' : toast && toast.type === 'warning' ? 'bg-theme-status-warning/20 text-theme-status-warning border border-theme-status-warning/30' : 'bg-theme-status-info/20 text-theme-status-info border border-theme-status-info/30'">
      <!-- Content -->
      <div class="ml-3 text-sm font-medium"
           x-text="toast && toast.message ? toast.message : 'Notification'"></div>
      <!-- Close Button -->
      <button @click="remove(index)"
              class="ml-auto -mx-1.5 -my-1.5 rounded-lg focus:ring-2 p-1.5 inline-flex items-center justify-center h-8 w-8"
              :class="toast && toast.type === 'success' ? 'hover:bg-theme-status-success/20 focus:ring-theme-status-success/30' : toast && toast.type === 'error' ? 'hover:bg-theme-status-error/20 focus:ring-theme-status-error/30' : toast && toast.type === 'warning' ? 'hover:bg-theme-status-warning/20 focus:ring-theme-status-warning/30' : 'hover:bg-theme-status-info/20 focus:ring-theme-status-info/30'"
              aria-label="Close">
        <span class="sr-only">Close</span>
        <svg class="w-3 h-3"
             aria-hidden="true"
             xmlns="http://www.w3.org/2000/svg"
             fill="none"
             viewBox="0 0 14 14">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6" />
        </svg>
      </button>
    </div>
  </template>
</div>
