{% load i18n %}

<div class="flex items-center gap-1 justify-center" id="goods-received-note-item-actions-{{ record.pk }}">
  {% if record.goods_received_note.status == 'New' or record.goods_received_note.status == 'Partially Received' %}
    <button
      hx-get="{% url 'receives:goods_received_notes:item_receive_form' record.pk %}"
      hx-target="#modal-form-content"
      hx-swap="innerHTML"
      class="p-1 text-theme-status-success hover:text-theme-status-success/80 transition-colors duration-200 cursor-pointer"
      title="{% translate 'Receive Item' %}"
      @click="$dispatch('open-modal', { title: '{% trans 'Receive Item' %}' })">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor"
           stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M14 3v4a1 1 0 0 0 1 1h4"/>
        <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"/>
        <path d="M9 17h6"/>
        <path d="M9 13h6"/>
      </svg>
    </button>
    <button
      hx-get="{% url 'receives:goods_received_notes:item_reject_form' record.pk %}"
      hx-target="#modal-form-content"
      hx-swap="innerHTML"
      class="p-1 text-gray-600 hover:text-gray-800 transition-colors duration-200 cursor-pointer"
      title="{% translate 'Reject Item' %}"
      @click="$dispatch('open-modal', { title: '{% trans 'Reject Item' %}' })">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor"
           stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M14 3v4a1 1 0 0 0 1 1h4"/>
        <path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"/>
        <path d="M9.5 10.5l5 5"/>
        <path d="M14.5 10.5l-5 5"/>
      </svg>
    </button>
    {% if record.get_received_quantity == 0 %}
      {% with item_count=record.goods_received_note.goodsreceivednoteitem_set.count %}
        {% if item_count > 1 %}
          {# Delete Button - Only show when there's more than one item #}
          <button
            hx-get="{% url 'receives:goods_received_notes:item_delete_form' record.pk %}"
            hx-target="#modal-form-content"
            hx-swap="innerHTML"
            class="p-1 text-red-600 hover:text-red-800 cursor-pointer"
            title="{% translate 'Delete Item' %}"
            @click="$dispatch('open-modal', { title: '{% trans 'Delete GRN Item' %}' })">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none"
                 stroke="currentColor"
                 stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M18 6L6 18"></path>
              <path d="M6 6l12 12"></path>
            </svg>
          </button>
        {% endif %}
      {% endwith %}
    {% endif %}
  {% endif %}

  {# Put Away Button - Only show when GRN is completed and item has remaining quantity to put away #}
  {% if record.goods_received_note.status == 'Completed' and record.get_received_quantity > 0 and record.get_received_quantity > record.get_put_away_quantity %}
    <button
      hx-get="{% url 'receives:goods_received_notes:item_put_away_form' record.pk %}"
      hx-target="#modal-form-content"
      hx-swap="innerHTML"
      class="p-1 text-theme-status-warning hover:text-theme-status-warning/80 transition-colors duration-200 cursor-pointer"
      title="{% translate 'Put Away Item' %}"
      @click="$dispatch('open-modal', { title: '{% trans 'Put Away Item' %}' })">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor"
           stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
        <polyline points="9,22 9,12 15,12 15,22"></polyline>
      </svg>
    </button>
  {% endif %}
</div>
