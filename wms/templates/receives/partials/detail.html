{% load i18n %}

{% block content %}
  {% if object %}
    <section class="mx-auto border-1 [&:not(:first-child)]:border-t-0 border-theme-input-border-secondary">
      <div
        class="py-2 px-3 border-b-1 min-h-12 bg-theme-table-header border-theme-input-border-secondary font-semibold uppercase tracking-wider flex justify-between items-center">
        <span>{% trans "Basic Information" %}</span>
        <div class="flex gap-4">
          {% if goods_received_note.status == "New" and goods_received_note.all_items_in_new_status %}
            <a href="{% url 'receives:goods_received_notes:update' goods_received_note.pk %}"
               class="inline-flex items-center px-3 py-1 text-sm bg-theme-action-update text-white rounded hover:bg-theme-action-update-hover transition-colors">
              {% trans "Update" %}
            </a>
          {% endif %}

          {% if goods_received_note.status == "New" or goods_received_note.status == "Partially Received" %}
            <button
              hx-get="{% url 'receives:goods_received_notes:obsolete_form' goods_received_note.pk %}"
              hx-target="#modal-form-content"
              hx-swap="innerHTML"
              class="inline-flex items-center px-3 py-1 text-sm bg-theme-status-error text-white rounded hover:bg-theme-status-error/80 transition-colors cursor-pointer"
              title="{% translate 'Mark as Obsolete' %}"
              @click="$dispatch('open-modal', { title: '{% trans 'Mark as Obsolete' %}' })">
              {% trans "Obsolete" %}
            </button>
            <button
              hx-get="{% url 'receives:goods_received_notes:delete_form' goods_received_note.pk %}"
              hx-target="#modal-form-content"
              hx-swap="innerHTML"
              class="inline-flex items-center px-3 py-1 text-sm bg-theme-status-error text-white rounded hover:bg-theme-status-error/80 transition-colors"
              title="{% translate 'Delete Goods Received Note' %}"
              @click="$dispatch('open-modal', { title: '{% trans 'Delete Goods Received Note' %}' })">
              {% trans "Delete" %}
            </button>
          {% endif %}
          {% if goods_received_note.status == "Completed" %}
            <a href="{% url 'pdfs:receives:arrival_notice_pdf' goods_received_note.pk %}"
               target="_blank"
               class="inline-flex items-center justify-center px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition">
              <svg xmlns="http://www.w3.org/2000/svg"
                   class="h-6 w-6 mr-2"
                   viewBox="0 0 24 24"
                   fill="none"
                   stroke="currentColor"
                   stroke-width="2"
                   stroke-linecap="round"
                   stroke-linejoin="round">
                <polyline points="6 9 6 2 18 2 18 9"></polyline>
                <path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"></path>
                <rect x="6" y="14" width="12" height="8"></rect>
              </svg>
              Arrival Notice
            </a>
          {% endif %}
        </div>
      </div>
      <div class="bg-white rounded-none overflow-hidden">
        <!-- Main Information Card -->
        <div class="p-0">
          <div class="space-y-6">
            <!-- Goods Received Note Information Section -->
            <div class="bg-theme-bg-secondary/10 rounded-none">
              <div class="grid grid-cols-2">
                <!-- Left Content -->
                <div class="grid grid-cols-1 divide-y divide-gray-200 ">
                  {% include "_components/detail_field.html" with label="System Number" value=goods_received_note.system_number %}
                  {% include "_components/detail_field.html" with label="Issued By" value=goods_received_note.issued_by %}
                  {% include "_components/detail_field.html" with label="Consignor" value=goods_received_note.consignor %}
                  <div
                    class="flex border-b-1 [&:last-child]:border-b-0 [&:only-child]:border-b-1 rounded-none border-gray-200">
                    <label
                      class="flex items-center p-3 justify-end text-end text-theme-text-primary text-sm w-36 font-medium bg-theme-form-label">
                      {% trans "Status" %}
                    </label>
                    <span class="flex items-center p-3 text-theme-text-primary bg-theme-form-span flex-1">
                      {{ goods_received_note.html_status_display }}
                    </span>
                  </div>
                  <div
                    class="flex border-b-1 [&:last-child]:border-b-0 [&:only-child]:border-b-1 rounded-none border-gray-200">
                    <label
                      class="flex items-center p-3 justify-end text-end text-theme-text-primary text-sm w-36 font-medium bg-theme-form-label">
                      {% trans "Put Away" %}
                    </label>
                    <span class="flex items-center p-3 text-theme-text-primary bg-theme-form-span flex-1">
                      {{ goods_received_note.html_put_away_status_display }}
                    </span>
                  </div>
                  {% include "_components/detail_field.html" with label="Arrival Date" value=goods_received_note.arrival_datetime|date:"Y-m-d g:i A" %}
                  {% include "_components/detail_field.html" with label="Deliver To" value=goods_received_note.deliver_to|default:"-" %}
                  {% include "_components/detail_field.html" with label="Reference" value=goods_received_note.customer_reference|default:"-" %}
                  {% include "_components/detail_field.html" with label="Inbound Delivery No" value=goods_received_note.consignor_inbound_delivery_no|default:"-" %}
                  {% include "_components/detail_field.html" with label="Remark" value=goods_received_note.remark|default:"-" %}
                </div>
                <!-- Right Content -->
                <div class="grid grid-cols-1 divide-y divide-gray-200 ">
                  {% include "_components/detail_field.html" with label="Container Date" value=goods_received_note.container_date|default:"-" %}
                  {% include "_components/detail_field.html" with label="Container Number" value=goods_received_note.container_number|default:"-" %}
                  {% include "_components/detail_field.html" with label="Container Size" value=goods_received_note.container_size|default:"-" %}
                  {% include "_components/detail_field.html" with label="Container Seal Number" value=goods_received_note.container_seal_number|default:"-" %}
                  {% include "_components/detail_field.html" with label="No. Of Pallet" value=goods_received_note.container_no_of_pallet|default:"-" %}
                  {% include "_components/detail_field.html" with label="No. Of Loose Carton" value=goods_received_note.container_no_of_loose_carton|default:"-" %}
                  {% include "_components/detail_field.html" with label="Dimension (Length)" value=goods_received_note.container_length|default:"-" %}
                  {% include "_components/detail_field.html" with label="Dimension (Width)" value=goods_received_note.container_width|default:"-" %}
                  {% include "_components/detail_field.html" with label="Dimension (Height)" value=goods_received_note.container_height|default:"-" %}
                  {% include "_components/detail_field.html" with label="Dimension Unit" value=goods_received_note.container_dimension_unit|default:"-" %}
                  {% include "_components/detail_field.html" with label="M3" value=goods_received_note.container_cubicmeter|default:"-" %}
                  {% include "_components/detail_field.html" with label="Gross weight / pallet (KG)" value=goods_received_note.container_gross_weight_kg|default:"-" %}
                  {% include "_components/detail_field.html" with label="Gross weight (T)" value=goods_received_note.container_gross_weight_tonn|default:"-" %}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- Goods Received Note Items Section -->
    <section class="mx-auto mt-4 border-0 border-theme-input-border-secondary">
      <div class="flex justify-end gap-4">
        {% if goods_received_note.status == "New" or goods_received_note.status == "Partially Received" %}
          <button
            hx-get="{% url 'receives:goods_received_notes:item_add_form' goods_received_note.pk %}"
            hx-target="#modal-form-content"
            hx-swap="innerHTML"
            class="inline-flex items-center px-3 py-1 text-sm bg-theme-primary text-white rounded hover:bg-theme-primary-hover transition-colors cursor-pointer"
            title="{% translate 'Add New Item' %}"
            @click="$dispatch('open-modal', { title: '{% trans 'Add New Item' %}' })">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd"
                    d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                    clip-rule="evenodd"/>
            </svg>
            {% trans "Add Item" %}
          </button>
        {% endif %}
      </div>
      <div class="bg-white overflow-hidden">
        <!-- partial table content -->
        <div hx-get="{% url 'receives:goods_received_notes:item_list' goods_received_note.pk %}?hxf=1"
             hx-trigger="load"
             hx-swap="outerHTML">{% include '_components/loading_spinner.html' %}</div>
      </div>
    </section>
  {% endif %}
{% endblock content %}
