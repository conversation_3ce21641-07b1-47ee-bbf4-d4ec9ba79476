{% extends 'base.html' %}

{% load crispy_forms_tags %}
{% load render_table from django_tables2 %}

{% block content %}
  <section class="lg:col-span-6 mb-6 pt-2">
    {% include 'tables/title_section.html' %}
    <!-- Filters Section -->
    {% include "tables/filter.html" %}
    {% if customize_export_wro_excel_url or customize_export_grn_excel_url %}
      <div class="flex justify-end gap-4">
      <a id="export-excel-button"
         href="{% if customize_export_grn_excel_url %}{% url 'reports:export_grn_report_xlsx' %}{% elif customize_export_wro_excel_url %}{% url 'reports:export_wro_report_xlsx' %}{% else %}#{% endif %}?{{ request.GET.urlencode }}"
         target="_blank"
         class="inline-flex items-center justify-center px-2 py-1 bg-theme-status-success text-white rounded hover:bg-theme-status-success transition">
        <svg xmlns="http://www.w3.org/2000/svg"
             fill="none"
             viewBox="0 0 24 24"
             stroke-width="1.5"
             stroke="currentColor"
             class="h-5 w-5 mr-2"
        >
          <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m.75 12 3 3m0 0 3-3m-3 3v-6m-1.5-9H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
        </svg>
        Export Customized Excel
      </a>
    </div>

    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Update export button when the page loads
        setTimeout(updateExportButtonUrl, 500); // Delay to ensure Select2 is initialized

        // Update export button when filters change
        document.addEventListener('htmx:afterSwap', function(event) {
          // Check if the swap target is the table content
          if (event.detail.target.id === 'table-content') {
            setTimeout(updateExportButtonUrl, 100); // Small delay to ensure DOM is updated
          }
        });

        // Listen for Select2 changes
        $(document).on('select2:select select2:unselect', function() {
          setTimeout(updateExportButtonUrl, 100);
        });

        // Listen for form input changes
        document.addEventListener('input', function(event) {
          if (event.target.form && event.target.form.id === 'filter-form') {
            setTimeout(updateExportButtonUrl, 100);
          }
        });

        // Function to update the export button URL with current filter values
        function updateExportButtonUrl() {
          const exportButton = document.getElementById('export-excel-button');
          if (!exportButton) return;

          const filterForm = document.getElementById('filter-form');
          if (!filterForm) return;

          // Get the base URL
          const baseUrl = exportButton.getAttribute('href').split('?')[0];

          // Create a URLSearchParams object
          const params = new URLSearchParams();

          // Process all form elements
          const formElements = filterForm.elements;
          for (let i = 0; i < formElements.length; i++) {
            const element = formElements[i];

            // Skip buttons and elements without name
            if (!element.name || element.type === 'button' || element.type === 'submit') continue;

            // Handle different input types
            if (element.type === 'checkbox' || element.type === 'radio') {
              if (element.checked) {
                params.append(element.name, element.value);
              }
            }
            // Handle select elements (including multiple)
            else if (element.tagName === 'SELECT') {
              // Handle multiple select
              if (element.multiple) {
                const selectedOptions = Array.from(element.selectedOptions);
                for (const option of selectedOptions) {
                  if (option.value) {
                    params.append(element.name, option.value);
                  }
                }
              }
              // Handle Select2 multiple
              else if ($(element).hasClass('django-select2') && $(element).prop('multiple')) {
                const selectedValues = $(element).val();
                if (selectedValues && Array.isArray(selectedValues)) {
                  for (const value of selectedValues) {
                    if (value) {
                      params.append(element.name, value);
                    }
                  }
                }
              }
              // Handle regular select and Select2 single
              else if (element.value) {
                params.append(element.name, element.value);
              }
            }
            // Handle date range inputs (special case for django-filter DateFromToRangeFilter)
            else if (element.name.endsWith('_after') || element.name.endsWith('_before')) {
              // For date range filters, we need to convert the field names
              // from date_range_after/date_range_before to date_range_after/date_range_before
              if (element.value) {
                // Map field names to the format expected by the export function
                const fieldName = element.name;
                params.append(fieldName, element.value);
              }
            }
            // Handle all other input types
            else if (element.value) {
              params.append(element.name, element.value);
            }
          }

          // Add search query if present
          const searchInput = document.getElementById('query-search');
          if (searchInput && searchInput.value) {
            params.append('query', searchInput.value);
          }

          // Update the export button href
          exportButton.setAttribute('href', `${baseUrl}?${params.toString()}`);
          console.log('Export URL updated:', exportButton.getAttribute('href'));
        }
      });
    </script>
    {% endif %}
    {% include 'tables/control_section.html' %}
    <div id="table-content">{% render_table table %}</div>
  </section>
{% endblock content %}
