{% extends "base.html" %}

{% load static %}
{% load django_tables2 %}

{% block content %}
  <div class="flex flex-col lg:grid md:grid-cols-8 gap-4 pt-6"
       x-data="{ showLeftPanel: false }">
    <!-- Left panel - DataTable -->
    <div class="w-full md:col-span-2 transition-all duration-300 ease-in-out"
         x-show="showLeftPanel"
         x-cloak>
      <div class="bg-theme-bg-primary h-full">
        <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
          {% include "tables/filter_auto.html" %}
        </div>
        <!-- Search -->
        <div class="w-full flex flex-col flex-row justify-between md:justify-end items-center gap-4">
          {% include "tables/search.html" %}
          {% include "tables/per_page.html" %}
        </div>
        <div class="py-4" id="table-content">{% include "tables/table_htmx.html" with table=table %}</div>
      </div>
    </div>
    <!-- Right panel - Detail View -->
    <div class="w-full transition-all duration-300 ease-in-out"
         :class="showLeftPanel ? 'md:col-span-6' : 'md:col-span-8'">
      <div class="bg-theme-bg-primary p-0">
        <!-- Toggle button for left panel -->
        <div class="mb-4">
          <button
            @click="showLeftPanel = !showLeftPanel"
            class="px-3 py-1 bg-theme-action-primary text-white rounded-md hover:bg-theme-action-primary-hover transition-colors duration-200"
          >
            <span x-text="showLeftPanel ? 'Hide Table' : 'Show Table'"></span>
          </button>
        </div>
        <div id="detail-panel">
          {% if object %}
            {{ detail_content|safe }}
          {% else %}
            <p class="text-gray-500">Select an item to view details</p>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
  <!-- Alpine.js styles -->
  <style>
    [x-cloak] {
      display: none !important;
    }
  </style>
{% endblock %}
