from django.conf import settings
from wms.cores.models import DISPLAY_EMPTY_VALUE
from wms.apps.users.forms import ConsignorFilterForm


def settings_context(_request):
    """Settings available by default to the templates context."""
    # Note: we intentionally do NOT expose the entire settings
    # to prevent accidental leaking of sensitive information
    return {
        "DEBUG": settings.DEBUG,
        "DISPLAY_EMPTY_VALUE": DISPLAY_EMPTY_VALUE,
    }


def consignor_filter_form(request):
    if request.user.is_authenticated:
        form = ConsignorFilterForm(initial={'consignor_filter': request.user.consignor_filter})
        return {'consignor_filter_form': form}
    return {}
