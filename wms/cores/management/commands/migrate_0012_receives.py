import psycopg2
from psycopg2.extras import execute_values
from django.core.management.base import BaseCommand


# Database connection settings
DB_SINOFLEX_CONFIG = {
    "dbname": "sinoflex",
    "user": "sinoflex",
    "password": "izWAe^RxJQyLWB!SFhptjr3y",
    "host": "localhost",
    "port": "5432",
}

DB_WMS_CONFIG = {
    "dbname": "wms_db",
    "user": "superpsql",
    "password": "password",
    "host": "localhost",
    "port": "5432",
}

table_list = [
    {
        "table_name_map": {
            "from": "receives_goodsreceivednote",
            "to": "receives_goodsreceivednote",
        },
        "table_column_map": {
            "id": "id",
            "created": "created",
            "modified": "modified",
            "created_by_id": "created_by_id",
            "modified_by_id": "modified_by_id",
            "consignor_id": "consignor_id",
            "deliver_to_id": "deliver_to_id",
            "issued_by_id": "issued_by_id",
            "arrival_datetime": "arrival_datetime",
            "completion_datetime": "completion_datetime",
            "status": "status",
            "numbering": "system_number",
            "is_edi_xml_sent": "is_edi_confirmation_sent",
            "is_from_edi": "is_from_edi",
            "consignor_inbound_delivery_no": "consignor_inbound_delivery_no",
            "customer_reference": "customer_reference",
            "imported_file": "imported_file",
            "remark": "remark",
            "container_seal_number": "container_seal_number",
            "container_dimension_unit": "container_dimension_unit",
            "container_number": "container_number",
            "container_size": "container_size",
            "container_cubicmeter": "container_cubicmeter",
            "container_gross_weight_kg": "container_gross_weight_kg",
            "container_gross_weight_tonn": "container_gross_weight_tonn",
            "container_date": "container_date",
            "container_no_of_pallet": "container_no_of_pallet",
            "container_no_of_loose_carton": "container_no_of_loose_carton",
            "container_length": "container_length",
            "container_width": "container_width",
            "container_height": "container_height",
        },
        "default_false_columns": ["is_put_away"]  # Add missing boolean columns
    },
    {
        "table_name_map": {
            "from": "receives_goodsreceivednoteitem",
            "to": "receives_goodsreceivednoteitem",
        },
        "table_column_map": {
            "id": "id",
            "created": "created",
            "modified": "modified",
            "created_by_id": "created_by_id",
            "modified_by_id": "modified_by_id",
            "sort_order": "sort_order",
            "goods_received_note_id": "goods_received_note_id",
            "status": "status",
            "item_id": "item_id",
            "is_serial_no": "is_serial_no",
            "quantity": "quantity",
            "uom_id": "uom_id",
            "batch_no": "batch_no",
            "expiry_date": "expiry_date",
            "shipment_number": "shipment_number",
            "pallet_number": "pallet_number",
            "remark": "remark",
        }
    },
    {
        "table_name_map": {
            "from": "receives_goodsreceivednotestockin",
            "to": "receives_goodsreceivednotestockin",
        },
        "table_column_map": {
            "id": " id",
            "created": " created",
            "modified": " modified",
            "created_by_id": " created_by_id",
            "modified_by_id": " modified_by_id",
            "transaction_id": " transaction_id",
            "goods_received_note_item_id": " goods_received_note_item_id",
            "stock_in_datetime": " stock_in_datetime",
            "approved_by_id": " approved_by_id",
            "deliver_to_id": " deliver_to_id",
            "item_id": " item_id",
            "approved_quantity": " approved_quantity",
            "uom_id": "uom_id",
            "batch_no": " batch_no",
            "expiry_date": " expiry_date",
            "converted_uom_id": " converted_uom_id",
            "converted_quantity": " converted_quantity",
            "remark": " remark",
            "unit_price": " unit_price",
            "total_price": " total_price",
        }
    },
    {
        "table_name_map": {
            "from": "receives_goodsreceivednotedefect",
            "to": "receives_goodsreceivednotedefect",
        },
        "table_column_map": {
            "id": "id",
            "created": "created",
            "modified": "modified",
            "created_by_id": "created_by_id",
            "modified_by_id": "modified_by_id",
            "goods_received_note_item_id": "goods_received_note_item_id",
            "rejected_by_id": "rejected_by_id",
            "reason": "reason",
            "uom_id": "uom_id",
            "converted_rejected_quantity": "converted_rejected_quantity",
            "defect_datetime": "defect_datetime",
            "is_migrated": "is_migrated",
            "converted_rejected_uom_id": "converted_rejected_uom_id",
            "deliver_to_id": "deliver_to_id",
            "item_id": "item_id",
            "rejected_quantity": "rejected_quantity",
            "batch_no": "batch_no",
            "remark": "remark",
        }
    },
    {
        "table_name_map": {
            "from": "receives_goodsreceivednotedefectstockin",
            "to": "receives_goodsreceivednotedefectstockin",
        },
        "table_column_map": {
            "id": "id",
            "created": "created",
            "modified": "modified",
            "created_by_id": "created_by_id",
            "modified_by_id": "modified_by_id",
            "transaction_id": "transaction_id",
            "goods_received_note_item_id": "goods_received_note_item_id",
            "uom_id": "uom_id",
            "expiry_date": "expiry_date",
            "stock_in_datetime": "stock_in_datetime",
            "approved_quantity": "approved_quantity",
            "unit_price": "unit_price",
            "total_price": "total_price",
            "converted_quantity": "converted_quantity",
            "approved_by_id": "approved_by_id",
            "converted_uom_id": "converted_uom_id",
            "deliver_to_id": "deliver_to_id",
            "item_id": "item_id",
            "batch_no": "batch_no",
            "reason": "reason",
            "remark": "remark",
        }
    },
]


class Command(BaseCommand):
    """Run the Migrate Data command.

    This command is to migrate All existing data from Old DB to New DB

    """

    help = "Migrate Data To Warehouse Management System"

    def handle(self, *args, **options):
        """Main function to handle data migration"""
        self.stdout.write("Starting data migration...")

        for table_dict in table_list:
            data = self.fetch_data(table_dict)
            if data:
                self.insert_data(data, table_dict)
                self.reset_sequence(table_dict)
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Successfully migrated {len(data)} rows from {table_dict["table_name_map"]["from"]} "
                        f"to {table_dict["table_name_map"]["to"]}."
                    )
                )
            else:
                self.stdout.write(self.style.WARNING("No data found in source table."))


    def fetch_data(self, table_dict=None):
        """Fetch selected columns from db_a"""
        source_columns = ", ".join(table_dict["table_column_map"].keys())  # Get source columns
        query = f"SELECT {source_columns} FROM {table_dict["table_name_map"]["from"]};"

        with psycopg2.connect(**DB_SINOFLEX_CONFIG) as conn:
            with conn.cursor() as cursor:
                cursor.execute(query)
                data = cursor.fetchall()

        # Convert JSON fields into a string format
        json_columns = table_dict.get("json_columns", [])  # Get JSON fields (if any)
        formatted_data = []
        for row in data:
            row = list(row)  # Convert tuple to list for modification
            for index, column_name in enumerate(table_dict["table_column_map"].keys()):
                if column_name in json_columns and isinstance(row[index], dict):
                    row[index] = json.dumps(row[index])  # Convert dict to JSON string
            formatted_data.append(tuple(row))  # Convert back to tuple

        return formatted_data

    def insert_data(self, data, table_dict=None):
        """Insert selected data into db_b with column mapping"""
        dest_columns = list(table_dict["table_column_map"].values())  # Get destination columns
        default_false_columns = table_dict.get("default_false_columns", [])  # Get boolean columns to default to False

        # Add missing boolean columns if they are not in the original column mapping
        for col in default_false_columns:
            if col not in dest_columns:
                dest_columns.append(col)

        placeholders = ", ".join(["%s"] * len(dest_columns))  # Generate %s placeholders

        updated_data = []
        for row in data:
            row_dict = dict(zip(table_dict["table_column_map"].values(), row))  # Convert row to dict

            # Assign False to any missing boolean columns
            for col in default_false_columns:
                row_dict.setdefault(col, False)

            updated_data.append(tuple(row_dict[col] for col in dest_columns))  # Convert back to tuple

        query = f"""
        INSERT INTO {table_dict["table_name_map"]["to"]} ({", ".join(dest_columns)}) VALUES %s
        ON CONFLICT (id) DO UPDATE SET
        {", ".join([f"{col} = EXCLUDED.{col}" for col in dest_columns if col != "id"])}
        RETURNING id;
        """

        with psycopg2.connect(**DB_WMS_CONFIG) as conn:
            with conn.cursor() as cursor:
                execute_values(cursor, query, updated_data)
            conn.commit()

    def reset_sequence(self, table_dict):
        """Reset sequence for auto-incrementing PK in db_b"""
        query = f"""
        SELECT setval(pg_get_serial_sequence('{table_dict["table_name_map"]["to"]}', 'id'),
                      COALESCE((SELECT MAX(id) FROM {table_dict["table_name_map"]["to"]}), 1), false);
        """

        with psycopg2.connect(**DB_WMS_CONFIG) as conn:
            with conn.cursor() as cursor:
                cursor.execute(query)
            conn.commit()
