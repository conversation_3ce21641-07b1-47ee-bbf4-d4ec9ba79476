(function() {
  // Define constants
  const DEFAULT_PLACEHOLDER = 'Select rack locations...';

  // Initialize the form immediately
  setTimeout(function() {
    initializePutAwayForm();
  }, 100); // Small delay to ensure DOM is ready

  // Handle form validation errors
  document.addEventListener('htmx:beforeSwap', function(evt) {
    // If the response contains a form with errors, we want to clean up Select2 instances
    if (evt.detail.xhr.status === 200 &&
        evt.detail.xhr.responseText.includes('put-away-racks') &&
        evt.detail.xhr.responseText.includes('text-red-500')) {

      // Clean up Select2 instances to prevent duplicates
      const selects = ['put-away-racks'];
      selects.forEach(className => {
        const selectElements = document.querySelectorAll('.' + className);
        selectElements.forEach(select => {
          if (select && $(select).data('select2')) {
            try {
              $(select).select2('destroy');
            } catch (e) {
              console.log(`Select2 not initialized yet or already destroyed for ${className}`);
            }
          }
        });
      });
    }
  });

  // Re-initialize after HTMX swap
  document.addEventListener('htmx:afterSwap', function(evt) {
    // Check if this is a put-away form swap
    if (evt.detail.xhr.responseText && evt.detail.xhr.responseText.includes('put-away-racks')) {
      setTimeout(function() {
        initializePutAwayForm();
      }, 100);
    }
  });

  function initializePutAwayForm() {
    // Initialize Select2 for multi-select rack dropdown
    const rackMultiSelect = document.querySelector('.put-away-racks');
    if (rackMultiSelect && !$(rackMultiSelect).data('select2')) {
      $(rackMultiSelect).select2({
        placeholder: DEFAULT_PLACEHOLDER,
        allowClear: true,
        closeOnSelect: false, // Keep dropdown open for multiple selections
        dropdownParent: $('#modal-form') // Ensure dropdown appears in modal
      });
    }

    // Focus on quantity field if it exists
    const quantityField = document.getElementById('id_quantity');
    if (quantityField) {
      quantityField.focus();
    }
  }

})();
